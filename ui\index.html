<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍵 可爱茶铺 - 古风经营小游戏</title>
    <link rel="stylesheet" href="style.css">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#4CAF50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="可爱茶铺">
</head>
<body>
    <!-- 顶部区域 -->
    <header class="cute-header">
        <div class="coins-bubble">
            <span class="coin-icon">🪙</span>
            <span class="coin-count" id="coin-count">100</span>
        </div>
        <div class="title-container">
            <h1 class="cute-title">🍵 可爱茶铺</h1>
            <div class="subtitle">古风经营小游戏</div>
        </div>
        <button class="menu-bubble" id="menu-button">
            <span class="menu-icon">☰</span>
        </button>
    </header>

    <!-- 信息卡片区域 -->
    <section class="info-cards">
        <div class="info-card weather-card">
            <div class="card-header">
                <span class="weather-icon" id="weather-icon">☀️</span>
                <span class="season-text" id="season-text">春天 · 晴天</span>
            </div>
            <div class="day-info">第 <span class="day-number" id="day-number">1</span> 天</div>
            <div class="weather-buttons" style="margin-top: 8px; display: flex; gap: 8px;">
                <button class="status-btn" id="shop-btn" title="打开商店">🏪</button>
                <button class="status-btn" id="inventory-btn" title="查看库存">🧺</button>
            </div>
        </div>

        <div class="info-card customer-card" id="customer-card">
            <div class="customer-avatar" id="customer-avatar">😊</div>
            <div class="customer-info">
                <div class="customer-name" id="customer-name">暂无顾客</div>
                <div class="customer-order" id="customer-order">等待顾客到来...</div>
                <div class="patience-bar" id="patience-bar" style="display: none;">
                    <div class="patience-fill" id="patience-fill"></div>
                    <span class="patience-text" id="patience-text">耐心：100%</span>
                </div>
            </div>
        </div>
    </section>

    <!-- 主要功能选项卡 -->
    <nav class="cute-tabs">
        <button class="tab-button active" data-tab="farm">
            <span class="tab-icon">🌱</span>
            <span class="tab-text">种植</span>
        </button>
        <button class="tab-button" data-tab="kitchen">
            <span class="tab-icon">🍳</span>
            <span class="tab-text">厨房</span>
        </button>
        <button class="tab-button" data-tab="shop">
            <span class="tab-icon">🏪</span>
            <span class="tab-text">茶摊</span>
        </button>
    </nav>

    <!-- 种植页面 -->
    <section class="tab-content active" id="farm">
        <div class="section-title">
            <span class="title-icon">🌱</span>
            <h2>我的小农场</h2>
        </div>

        <div class="farm-grid" id="farm-grid">
            <!-- 田地将通过JavaScript动态生成 -->
        </div>

        <!-- 快捷操作按钮 -->
        <div class="quick-actions">
            <button class="action-button water" id="water-all-btn">
                <span class="action-icon">💧</span>
                <span class="action-text">浇水</span>
            </button>
            <button class="action-button fertilize" id="fertilize-all-btn">
                <span class="action-icon">🌿</span>
                <span class="action-text">施肥</span>
            </button>
            <button class="action-button basket" id="basket-btn">
                <span class="action-icon">🧺</span>
                <span class="action-text">篮子</span>
            </button>
        </div>
    </section>

    <!-- 厨房页面 -->
    <section class="tab-content" id="kitchen">
        <div class="section-title">
            <span class="title-icon">🍳</span>
            <h2>温馨小厨房</h2>
        </div>

        <!-- 炉灶区域 -->
        <div class="kitchen-area">
            <h3 class="area-title">🔥 炉灶制茶</h3>
            <div class="stoves-grid" id="stoves-grid">
                <!-- 炉灶将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 案板区域 -->
        <div class="kitchen-area">
            <h3 class="area-title">🔪 案板加工</h3>
            <div class="processing-board" id="processing-board">
                <div class="board-visual">
                    <div class="cutting-board">📋</div>
                    <div class="ingredients" id="processing-ingredients">🥕🌿</div>
                </div>
                <div class="processing-info" id="processing-info">
                    <div class="processing-status">点击选择加工配方</div>
                </div>
                <div class="processing-recipes" id="processing-recipes">
                    <!-- 加工配方将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </section>

    <!-- 茶摊页面 -->
    <section class="tab-content" id="shop">
        <div class="section-title">
            <span class="title-icon">🍵</span>
            <h2>温馨茶摊</h2>
        </div>

        <!-- 制作好的茶饮 -->
        <div class="tea-display" id="tea-display">
            <div class="no-tea-hint">
                <span class="hint-icon">🫖</span>
                <span class="hint-text">还没有制作好的茶饮哦～</span>
            </div>
        </div>

        <!-- 小料区域 -->
        <div class="toppings-area">
            <h3 class="area-title">🍯 可用小料</h3>
            <div class="toppings-grid" id="toppings-grid">
                <!-- 小料将通过JavaScript动态生成 -->
            </div>
        </div>
    </section>

    <!-- 浮动购物车按钮 -->
    <button class="floating-cart" id="cart-button">
        <span class="cart-icon">🛒</span>
        <span class="cart-badge" id="cart-badge">0</span>
    </button>

    <!-- 底部消息气泡 -->
    <div class="message-bubble" id="message-bubble">
        <span class="message-icon">💬</span>
        <span class="message-text" id="message-text">欢迎来到可爱茶铺！开始您的经营之旅吧～</span>
    </div>

    <!-- 菜单面板 -->
    <div class="menu-panel" id="menu-panel" style="display: none;">
        <div class="menu-header">
            <h3>🎮 游戏菜单</h3>
            <button class="close-btn" id="close-menu">×</button>
        </div>
        <div class="menu-content">
            <button class="menu-item" id="save-game-btn">
                <span class="menu-icon">💾</span>
                <span class="menu-text">保存游戏</span>
            </button>
            <button class="menu-item" id="load-game-btn">
                <span class="menu-icon">📁</span>
                <span class="menu-text">加载游戏</span>
            </button>
            <button class="menu-item" id="recipe-book-btn">
                <span class="menu-icon">📖</span>
                <span class="menu-text">配方大全</span>
            </button>
            <button class="menu-item" id="test-mode-btn">
                <span class="menu-icon">🧪</span>
                <span class="menu-text">测试模式</span>
            </button>
            <button class="menu-item" id="test-window-btn">
                <span class="menu-icon">🔬</span>
                <span class="menu-text">测试窗口</span>
            </button>
        </div>
    </div>

    <!-- 商店面板 -->
    <div class="shop-panel" id="shop-panel" style="display: none;">
        <!-- 商店内容将通过JavaScript动态生成 -->
    </div>

    <!-- 配方面板 -->
    <div class="recipe-panel" id="recipe-panel" style="display: none;">
        <!-- 配方内容将通过JavaScript动态生成 -->
    </div>

    <!-- 测试模式面板 -->
    <div class="test-panel" id="test-panel" style="display: none;">
        <!-- 测试内容将通过JavaScript动态生成 -->
    </div>

    <!-- 篮子选择面板 -->
    <div class="basket-panel" id="basket-panel" style="display: none;">
        <!-- 篮子内容将通过JavaScript动态生成 -->
    </div>

    <script src="script.js"></script>
</body>
</html>
