/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Microsoft YaHei", "SimSun", sans-serif;
    -webkit-tap-highlight-color: transparent;
}

body {
    background-color: #e6e6e6;
    color: #4a4a4a;
    line-height: 1.6;
    font-size: 14px;
    overflow-x: hidden;
    touch-action: manipulation;
    width: 100%;
    min-height: 100vh;
}

.container {
    width: 100%;
    height: 100%;
    margin: 0 auto;
    background-color: #f2f2f2;
    padding: 10px;
    display: flex;
    flex-direction: column;
    overflow: visible;
    min-height: 100vh;
}

/* 顶部样式 */
header {
    margin-bottom: 10px;
}

.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #e9e9e9;
    border-radius: 5px;
    position: relative;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.title {
    font-size: 14px;
    font-weight: bold;
    color: #595959;
    text-align: center;
}

.menu-btn {
    font-size: 22px;
    cursor: pointer;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #d9d9d9;
}

.coins-display {
    padding: 6px 8px;
    background-color: #e0e0e0;
    border-radius: 4px;
    font-weight: bold;
    color: #4a4a4a;
    font-size: 12px;
}

#coins-count {
    color: #808080;
}

/* 菜单面板 */
.menu-panel {
    position: absolute;
    top: 60px;
    right: 10px;
    background-color: #f2f2f2;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 10px;
    z-index: 1000;
    display: none;
    max-width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.menu-panel button {
    display: block;
    width: 100%;
    margin: 5px 0;
    padding: 8px;
    text-align: left;
    border: none;
    background-color: #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    color: #4a4a4a;
}

.serve-button {
    background-color: #808080 !important;
    color: #f2f2f2 !important;
}

/* 信息滑块区域 */
.info-swiper {
    margin-bottom: 10px;
    overflow: hidden;
}

.swiper-container {
    width: 100%;
    height: 60px;
    position: relative;
}

.swiper-wrapper {
    display: flex;
    transition: transform 0.3s ease;
    height: 100%;
}

.swiper-slide {
    flex: 0 0 100%;
    height: 100%;
    padding: 10px;
    background-color: #e9e9e9;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    overflow-y: auto;
}

/* 统一滑块背景色为浅灰色 */
.swiper-slide.weather-season,
.swiper-slide.message-log {
    background-color: #f0f0f0;
}

.slide-title {
    font-weight: bold;
    margin-bottom: 10px;
    text-align: center;
    padding-bottom: 5px;
    border-bottom: 1px solid #e0e0e0;
}

/* 分页指示器 */
.swiper-pagination {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}

.swiper-pagination-bullet {
    width: 8px;
    height: 8px;
    margin: 0 5px;
    border-radius: 50%;
    background-color: #ccc;
    display: inline-block;
}

.swiper-pagination-bullet-active {
    background-color: #4CAF50;
}

/* 表格样式 */
table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

td {
    padding: 5px;
    border-bottom: 1px solid #f0f0f0;
}

td:first-child {
    font-weight: bold;
    width: 60px;
}

/* 可点击物品样式 */
.clickable-item {
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.clickable-item:hover {
    background-color: #e0e0e0;
}

.clickable-item:active {
    background-color: #d0d0d0;
}

/* 消息区域 */
.message-content {
    height: 100px;
    overflow-y: auto;
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.message-content::-webkit-scrollbar {
    display: none;
}

.message {
    padding: 4px;
    margin-bottom: 4px;
    background-color: #e0e0e0;
    border-radius: 3px;
    font-size: 12px;
}

.message.error {
    background-color: #d9d9d9;
    color: #808080;
}

/* 种子信息 */
.seed-info {
    text-align: center;
    margin: 10px 0;
    padding: 8px;
    background-color: #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
}

/* 商店遮罩层 */
.shop-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    z-index: 999;
    display: none;
    animation: overlayFadeIn 0.3s ease-out;
}

@keyframes overlayFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 商店面板 - 重构版 */
.shop-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 95%;
    max-width: 450px;
    height: 90vh;
    max-height: 850px;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.25);
    z-index: 1000;
    display: none;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid #e0e0e0;
    animation: shopPanelAppear 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
}

@keyframes shopPanelAppear {
    from {
        opacity: 0;
        transform: translate(-50%, -60%) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.shop-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: linear-gradient(135deg, #7c4f3f, #8b5a3c);
    color: white;
    box-shadow: 0 4px 16px rgba(124, 79, 63, 0.3);
    position: relative;
}

.shop-header::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
}

.shop-header h2 {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.close-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    font-size: 20px;
    color: white;
    cursor: pointer;
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: rotate(90deg) scale(1.1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.close-btn:active {
    transform: rotate(90deg) scale(0.95);
}

/* 商店选项卡 - 重构版 */
.shop-tabs, .recipe-tabs, .game-tabs {
    display: flex;
    margin: 0 20px 20px 20px;
    background: rgba(124, 79, 63, 0.05);
    border-radius: 12px;
    padding: 4px;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

.tab-btn, .game-tab {
    flex: 1;
    background: transparent;
    border: none;
    padding: 12px 16px;
    text-align: center;
    color: #7c4f3f;
    position: relative;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin: 0 2px;
}

.tab-btn:hover, .game-tab:hover {
    background: rgba(124, 79, 63, 0.1);
    color: #6a4334;
    transform: translateY(-1px);
}

.tab-btn.active, .game-tab.active {
    background: linear-gradient(135deg, #7c4f3f, #8b5a3c);
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(124, 79, 63, 0.3);
    transform: translateY(-2px);
}

.tab-btn.active::after, .game-tab.active::after {
    display: none;
}

.tab-btn.active::before, .game-tab.active::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 8px;
    padding: 1px;
    background: linear-gradient(135deg, rgba(255,255,255,0.3), transparent);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
}

.tab-content, .game-content {
    display: none;
    flex: 1;
    overflow-y: auto;
}

.tab-content.active, .game-content.active {
    display: block;
}

/* 种子网格 - 重构版 */
.seed-grid, .item-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    padding: 0;
}

.seed-btn, .shop-item-btn {
    padding: 16px 12px;
    border: 2px solid rgba(124, 79, 63, 0.1);
    border-radius: 12px;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    color: #333;
    font-size: 13px;
    font-weight: 500;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    min-height: 70px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.seed-btn:hover, .shop-item-btn:hover {
    border-color: rgba(124, 79, 63, 0.3);
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.12);
}

.seed-btn:active, .shop-item-btn:active {
    transform: translateY(-1px) scale(0.98);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 点击波纹效果 - 重构版 */
.seed-btn::after, .shop-item-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(124, 79, 63, 0.3) 0%, transparent 70%);
    opacity: 0;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.4s ease-out;
}

.seed-btn:active::after, .shop-item-btn:active::after {
    width: 200px;
    height: 200px;
    opacity: 1;
    transition: all 0.1s ease-out;
}

/* 选中状态 - 重构版 */
.seed-btn.selected, .shop-item-btn.selected {
    background: linear-gradient(135deg, #7c4f3f, #8b5a3c);
    border-color: #7c4f3f;
    color: white;
    box-shadow: 0 6px 20px rgba(124, 79, 63, 0.4);
    transform: translateY(-3px);
}

.seed-btn.selected::before, .shop-item-btn.selected::before {
    content: '✓';
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(255, 255, 255, 0.9);
    color: #7c4f3f;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

/* 添加到购物车提示 - 重构版 */
.add-to-cart-hint {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 16px 24px;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    z-index: 2000;
    animation: cartHintAnimation 2s ease-in-out forwards;
    box-shadow: 0 8px 32px rgba(40, 167, 69, 0.4);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.add-to-cart-hint::before {
    content: '🛒';
    margin-right: 8px;
    font-size: 16px;
}

@keyframes cartHintAnimation {
    0% { 
        opacity: 0; 
        transform: translate(-50%, 30px) scale(0.8); 
    }
    15% { 
        opacity: 1; 
        transform: translate(-50%, 0) scale(1.05); 
    }
    20% { 
        transform: translate(-50%, 0) scale(1); 
    }
    85% { 
        opacity: 1; 
        transform: translate(-50%, 0) scale(1); 
    }
    100% { 
        opacity: 0; 
        transform: translate(-50%, -30px) scale(0.8); 
    }
}

/* 商店布局 - 重构版 */
.shop-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: transparent;
}

.shop-items-container {
    flex: 1 !important;
    overflow-y: auto !important;
    padding: 0 20px 300px 20px !important;
    -webkit-overflow-scrolling: touch !important;
    max-height: calc(100% - 140px) !important;
}

.shop-section {
    margin-bottom: 28px;
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    border: 1px solid rgba(124, 79, 63, 0.1);
    transition: all 0.3s ease;
}

.shop-section:hover {
    box-shadow: 0 8px 24px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}

.shop-section-title {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 16px;
    padding-bottom: 12px;
    color: #7c4f3f;
    position: relative;
    letter-spacing: 0.3px;
}

.shop-section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(135deg, #7c4f3f, #8b5a3c);
    border-radius: 1.5px;
}

.shop-section-title::before {
    content: '🏪';
    margin-right: 8px;
    font-size: 18px;
}

.cart-preview {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background: linear-gradient(135deg, #ffffff, #f8f9fa) !important;
    padding: 12px 16px !important;
    border-top: 2px solid #e0e0e0 !important;
    border-radius: 16px 16px 0 0 !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 8px !important;
    box-shadow: 0 -6px 20px rgba(0,0,0,0.15) !important;
    border: 1px solid #e9ecef !important;
    border-bottom: none !important;
    flex-shrink: 0 !important;
    height: 280px !important;
    margin: 0 !important;
    overflow: hidden !important;
    z-index: 10 !important;
}

/* 购物车预览头部 */
.cart-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 6px;
    border-bottom: 2px solid #e3f2fd;
    flex-shrink: 0;
    margin-bottom: 6px;
    background: linear-gradient(135deg, rgba(255,255,255,0.8), rgba(248,249,250,0.9));
    padding: 6px 10px;
    margin: -12px -16px 6px -16px;
    border-radius: 16px 16px 0 0;
}

.cart-preview-title {
    font-size: 16px;
    font-weight: 700;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.cart-preview-title::before {
    content: '🛒';
    font-size: 18px;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.2));
}

.cart-preview-total {
    font-weight: 600;
    color: #2e7d32;
    display: flex;
    flex-direction: column;
    gap: 3px;
    line-height: 1.3;
    text-align: right;
    background: linear-gradient(135deg, #e8f5e9, #f1f8e9);
    padding: 8px 12px;
    border-radius: 10px;
    border: 1px solid #c8e6c9;
    box-shadow: 0 2px 6px rgba(46, 125, 50, 0.1);
}

.cart-preview-total > div:first-child {
    font-size: 12px;
    color: #4a5568;
    font-weight: 500;
}

.cart-preview-total > div:last-child {
    font-size: 16px;
    color: #2e7d32;
    font-weight: 800;
    text-shadow: 0 1px 2px rgba(46, 125, 50, 0.2);
}

/* 购物车物品列表 */
.cart-preview-items {
    flex: 1 !important;
    overflow-y: auto !important;
    background: linear-gradient(135deg, #fafbfc, #f8f9fa) !important;
    border-radius: 12px !important;
    padding: 8px !important;
    border: 1px solid #e3f2fd !important;
    min-height: 120px !important;
    max-height: 140px !important;
    -webkit-overflow-scrolling: touch !important;
    box-shadow: inset 0 2px 8px rgba(0,0,0,0.05) !important;
}

.cart-preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    margin-bottom: 6px;
    background: linear-gradient(135deg, #ffffff, #fefefe);
    border-radius: 8px;
    font-size: 13px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.08);
    border: 1px solid #f0f4f8;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.cart-preview-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(135deg, #7c4f3f, #8b5a3c);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.cart-preview-item:hover::before {
    opacity: 1;
}

.cart-preview-item:last-child {
    margin-bottom: 0;
}

.cart-preview-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    border-color: #dee2e6;
}

.cart-preview-item-name {
    flex: 1;
    color: #333;
    font-weight: 500;
    margin-right: 8px;
    word-wrap: break-word;
    line-height: 1.3;
}

.cart-preview-item-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.cart-preview-item-quantity {
    color: #6c757d;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 10px;
    min-width: 20px;
    text-align: center;
}

.cart-preview-item-price {
    color: #7c4f3f;
    font-weight: 600;
    white-space: nowrap;
    font-size: 11px;
}

.cart-preview-empty {
    text-align: center;
    color: #8e9aaf;
    font-style: italic;
    padding: 24px 16px;
    font-size: 13px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    opacity: 0.8;
}

.cart-preview-empty::before {
    content: '🛒';
    font-size: 32px;
    opacity: 0.5;
    margin-bottom: 4px;
}

.cart-preview-button {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    color: white !important;
    border: none !important;
    padding: 14px 18px !important;
    border-radius: 12px !important;
    font-size: 15px !important;
    font-weight: 700 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3) !important;
    position: relative !important;
    overflow: hidden !important;
    width: 100% !important;
    white-space: nowrap !important;
    flex-shrink: 0 !important;
    min-height: 48px !important;
    margin-top: auto !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    border: 2px solid transparent !important;
}

.cart-preview-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.cart-preview-button:hover::before {
    width: 300px;
    height: 300px;
}

.cart-preview-button:hover {
    background: linear-gradient(135deg, #218838, #1e7e34);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.cart-preview-button:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.cart-preview-button:disabled {
    background: linear-gradient(135deg, #bdbdbd, #9e9e9e);
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    opacity: 0.7;
}

.cart-preview-button:disabled::before {
    display: none;
}

/* 购物车样式 - 优化版 */
.cart-items {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 15px;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;
    -webkit-overflow-scrolling: touch;
}

.empty-cart {
    text-align: center;
    color: #999;
    padding: 30px 20px;
    font-style: italic;
}

.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 8px;
    margin-bottom: 8px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.cart-item:last-child {
    margin-bottom: 0;
}

.cart-item:hover {
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    transform: translateY(-1px);
}

.cart-item-name {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-right: 12px;
    word-wrap: break-word;
    line-height: 1.3;
}

.cart-item-price {
    margin: 0 12px;
    color: #7c4f3f;
    font-weight: 600;
    font-size: 13px;
    white-space: nowrap;
}

.cart-item-quantity {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 6px;
    padding: 2px;
    border: 1px solid #e9ecef;
}

.quantity-btn {
    width: 28px;
    height: 28px;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6c757d;
}

.quantity-btn:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.quantity-btn:active {
    background-color: #dee2e6;
    transform: scale(0.95);
}

.quantity-value {
    margin: 0 8px;
    width: 30px;
    text-align: center;
    font-size: 13px;
    font-weight: 600;
    color: #495057;
}

.cart-item-remove {
    margin-left: 12px;
    color: #dc3545;
    font-size: 18px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.cart-item-remove:hover {
    opacity: 1;
    background-color: #f8d7da;
    color: #721c24;
}

.cart-total {
    text-align: right;
    font-weight: bold;
    margin-bottom: 15px;
    padding: 12px 8px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

#cart-total-amount {
    color: #7c4f3f;
    font-size: 16px;
}

.cart-buttons {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

#checkout-btn, #clear-cart-btn {
    padding: 12px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#checkout-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    flex: 2;
}

#checkout-btn:hover {
    background: linear-gradient(135deg, #218838, #1e7e34);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

#clear-cart-btn {
    background-color: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
    flex: 1;
}

#clear-cart-btn:hover {
    background-color: #e9ecef;
    color: #495057;
    border-color: #adb5bd;
}

/* 小篮子选择面板 */
.basket-select-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #f2f2f2;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    z-index: 1001;
    display: none;
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.basket-select-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
    gap: 15px;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

/* 顶部确认种植按钮样式 */
.confirm-plant-top-btn {
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    box-shadow: 0 3px 8px rgba(76, 175, 80, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.confirm-plant-top-btn::before {
    content: '🌱';
    margin-right: 6px;
    font-size: 16px;
}

.confirm-plant-top-btn:not(:disabled):hover {
    background: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(76, 175, 80, 0.4);
}

.confirm-plant-top-btn:not(:disabled):active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
}

.confirm-plant-top-btn:disabled {
    background: #e0e0e0;
    color: #999;
    cursor: not-allowed;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transform: none;
}

.confirm-plant-top-btn:disabled::before {
    content: '⌛';
}

/* 去购买按钮样式 */
.go-to-shop-btn {
    background: #ff9800;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    box-shadow: 0 3px 8px rgba(255, 152, 0, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.go-to-shop-btn::before {
    content: '🛒';
    margin-right: 6px;
    font-size: 16px;
}

.go-to-shop-btn:hover {
    background: linear-gradient(135deg, #f57c00, #ff9800);
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(255, 152, 0, 0.4);
}

.basket-select-title {
    font-weight: bold;
    font-size: 16px;
    color: #4a4a4a;
}

.basket-select-content {
    padding: 15px;
}

.basket-select-info {
    margin-bottom: 15px;
    padding: 8px 12px;
    background-color: #e9e9e9;
    border-radius: 5px;
    font-size: 14px;
}

.seed-selection-section {
    margin-bottom: 20px;
}

.seed-section-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #4a4a4a;
    font-size: 14px;
}

.available-seeds .seed-btn {
    background-color: #e0e0e0;
    color: #4a4a4a;
    position: relative;
    padding-left: 25px;
}

.unavailable-seeds .seed-btn {
    background-color: #f0f0f0;
    color: #999;
    position: relative;
    padding-left: 25px;
}

.unavailable-seeds .seed-btn::after {
    content: "↗";
    position: absolute;
    top: 2px;
    right: 4px;
    font-size: 12px;
    color: #666;
}

/* 种子图标样式 */
.seed-btn::before {
    position: absolute;
    left: 4px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
}

/* 各种种子的图标 */
.seed-btn[data-seed="五味子"]::before { content: "🫐"; }
.seed-btn[data-seed="乌梅"]::before { content: "🟫"; }
.seed-btn[data-seed="山楂"]::before { content: "🔴"; }
.seed-btn[data-seed="陈皮"]::before { content: "🍊"; }
.seed-btn[data-seed="甘草"]::before { content: "🌿"; }
.seed-btn[data-seed="桂花"]::before { content: "🌼"; }
.seed-btn[data-seed="大麦"]::before { content: "🌾"; }
.seed-btn[data-seed="菊花"]::before { content: "🌻"; }
.seed-btn[data-seed="金银花"]::before { content: "🌺"; }
.seed-btn[data-seed="决明子"]::before { content: "🌰"; }
.seed-btn[data-seed="枸杞"]::before { content: "🔴"; }
.seed-btn[data-seed="生姜"]::before { content: "🫚"; }
.seed-btn[data-seed="桂圆"]::before { content: "🟤"; }
.seed-btn[data-seed="红枣"]::before { content: "🟤"; }
.seed-btn[data-seed="薄荷"]::before { content: "🌿"; }
.seed-btn[data-seed="玫瑰花"]::before { content: "🌹"; }
.seed-btn[data-seed="洛神花"]::before { content: "🌺"; }
.seed-btn[data-seed="冬瓜"]::before { content: "🥒"; }
.seed-btn[data-seed="荷叶"]::before { content: "🍃"; }
.seed-btn[data-seed="薏米"]::before { content: "⚪"; }
.seed-btn[data-seed="雪花梨"]::before { content: "🍐"; }
.seed-btn[data-seed="话梅"]::before { content: "🟫"; }
.seed-btn[data-seed="甘蔗"]::before { content: "🌾"; }
.seed-btn[data-seed="柚子"]::before { content: "🍋"; }
.seed-btn[data-seed="柠檬"]::before { content: "🍋"; }
/* 新增种子图标 */
.seed-btn[data-seed="桑叶"]::before { content: "🌿"; }
.seed-btn[data-seed="杭白菊"]::before { content: "🌼"; }
.seed-btn[data-seed="水蜜桃"]::before { content: "🍑"; }
.seed-btn[data-seed="黄芪"]::before { content: "🌰"; }
.seed-btn[data-seed="白茅根"]::before { content: "🪴"; }
.seed-btn[data-seed="马蹄"]::before { content: "⚪"; }
.seed-btn[data-seed="糯米"]::before { content: "🌾"; }
.seed-btn[data-seed="米"]::before { content: "🌾"; }

/* 商店物品按钮图标 */
.shop-item-btn::before {
    margin-right: 8px;
    font-size: 18px;
}

.shop-item-btn[data-item="蜂蜜"]::before { content: "🍯"; }
.shop-item-btn[data-item="银耳"]::before { content: "🍄"; }
.shop-item-btn[data-item="红糖"]::before { content: "🟤"; }
.shop-item-btn[data-item="薄荷叶"]::before { content: "🌿"; }
.shop-item-btn[data-item="冰糖"]::before { content: "❄️"; }
.shop-item-btn[data-item="乌龙茶包"]::before { content: "🍃"; }

.basket-select-buttons {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.basket-select-buttons button {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
}

#confirm-plant-btn {
    background-color: #4CAF50;
    color: white;
}

#confirm-plant-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

#cancel-plant-btn {
    background-color: #e0e0e0;
    color: #4a4a4a;
}

/* 种植选择弹窗中的购物车区域样式 - 优化版 */
.basket-cart-section {
    margin-top: 18px;
    padding: 16px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 1px solid #dee2e6;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.basket-cart-title {
    font-size: 15px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 12px;
    text-align: center;
    position: relative;
    padding-bottom: 8px;
}

.basket-cart-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background: #7c4f3f;
    border-radius: 1px;
}

.basket-cart-items {
    max-height: 140px;
    overflow-y: auto;
    margin-bottom: 12px;
    -webkit-overflow-scrolling: touch;
}

.basket-cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 6px;
    background: white;
    border-radius: 8px;
    font-size: 13px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #f0f0f0;
    transition: all 0.2s ease;
}

.basket-cart-item:last-child {
    margin-bottom: 0;
}

.basket-cart-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    border-color: #dee2e6;
}

.basket-cart-item-name {
    flex: 1;
    color: #333;
    font-weight: 500;
    margin-right: 10px;
    word-wrap: break-word;
    line-height: 1.3;
}

.basket-cart-item-quantity {
    margin: 0 10px;
    color: #6c757d;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 11px;
    min-width: 25px;
    text-align: center;
}

.basket-cart-item-price {
    color: #7c4f3f;
    font-weight: 600;
    white-space: nowrap;
}

.basket-cart-total {
    text-align: center;
    font-weight: 700;
    color: #7c4f3f;
    font-size: 14px;
    border-top: 2px solid #dee2e6;
    padding-top: 12px;
    background: white;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.basket-cart-empty {
    text-align: center;
    color: #95a5a6;
    font-style: italic;
    padding: 20px;
    font-size: 13px;
    background: white;
    border-radius: 8px;
    border: 2px dashed #dee2e6;
}

/* 配方面板 */
.recipe-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    background-color: #f2f2f2;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    display: none;
    overflow: hidden;
    flex-direction: column;
    -webkit-overflow-scrolling: touch;
}

.recipe-items {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 15px;
    -webkit-overflow-scrolling: touch;
}

.recipe-item {
    padding: 12px;
    margin-bottom: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.recipe-item:hover {
    background-color: #f0f0f0;
}

.recipe-name {
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
    font-size: 14px;
}

.recipe-name::before {
    content: "🍵 ";
    font-size: 16px;
}

.recipe-ingredients {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

/* 游戏区域 */
.game-content {
    padding: 10px 0;
    flex: 1;
    overflow-y: auto;
    max-height: calc(100vh - 150px);
}

/* 农田区域 */
.farm-controls {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.farm-controls button {
    flex: 1;
    padding: 8px 5px;
    margin: 0 4px;
    border: none;
    border-radius: 4px;
    background-color: #f0f0f0;
    font-size: 12px;
}

.plot-selectors {
    margin-bottom: 10px;
}

.plots-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.plot {
    flex: 0 1 calc(50% - 10px);
    background-color: #e9e9e9;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 10px;
}

.plot-checkbox-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}

.plot-checkbox-container label {
    margin-left: 4px;
    font-weight: bold;
    font-size: 13px;
}

.plot-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.plot-row {
    display: flex;
}

.plot-label {
    flex: 1;
    font-weight: bold;
}

.plot-value {
    flex: 2;
}

/* 厨房区域 */
.section-title {
    position: relative;
    background-color: #f0f0f0;
    padding: 8px;
    margin-bottom: 8px;
    border-radius: 4px;
    font-weight: bold;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 34px;
    font-size: 14px;
}

.stoves {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    margin-bottom: 15px;
}

.stove {
    flex: 1;
    height: 70px;
    background-color: #e0e0e0;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    padding: 5px;
}

.stove-label {
    font-size: 11px;
    font-weight: bold;
    color: #666;
    margin-bottom: 2px;
}

.processing-board {
    height: 70px;
    background-color: #e0e0e0;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 12px;
    margin-bottom: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    padding: 5px;
}

.processing-label {
    font-size: 11px;
    font-weight: bold;
    color: #666;
    margin-bottom: 2px;
}

.processing-recipes {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.recipe-btn {
    flex: 1;
    padding: 8px 4px;
    margin: 2px;
    background-color: #e9e9e9;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    color: #4a4a4a;
    font-size: 12px;
    text-align: center;
    transition: all 0.2s ease;
    position: relative;
    min-height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
}

/* 原料不足状态（灰色） */
.recipe-btn.no-material {
    background-color: #f0f0f0 !important;
    border-color: #ccc !important;
    color: #999 !important;
    cursor: not-allowed;
    opacity: 0.7;
}

/* 原料不多状态（橙色警告） */
.recipe-btn.low-material {
    background-color: #fff3e0 !important;
    border-color: #ffab40 !important;
    color: #ff6b00 !important;
    font-weight: 600;
    animation: pulse 2s infinite;
}

/* 原料充足状态（绿色） */
.recipe-btn.enough-material {
    background-color: #e8f5e8 !important;
    border-color: #4caf50 !important;
    color: #2e7d32 !important;
    font-weight: 600;
}

@keyframes pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(255, 107, 0, 0.4);
    }
    50% {
        box-shadow: 0 0 0 4px rgba(255, 107, 0, 0);
    }
}

.recipe-btn[data-recipe="红糖"]::before {
    content: "🌾 ";
}

.recipe-btn[data-recipe="薄荷叶"]::before {
    content: "🌿 ";
}

.recipe-btn[data-recipe="姜丝"]::before {
    content: "🫚 ";
}

.recipe-btn[data-recipe="柚子丝"]::before {
    content: "🍊 ";
}

.recipe-btn[data-recipe="银耳丝"]::before {
    content: "🍄 ";
}

.recipe-btn[data-recipe="柠檬片"]::before {
    content: "🍋 ";
}

.basket-content {
    max-height: 150px;
    overflow-y: auto;
    background-color: #f9f9f9;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.basket-item {
    padding: 8px;
    margin-bottom: 6px;
    border-radius: 4px;
    font-size: 12px;
    position: relative;
    background-color: #f0f0f0;
    transition: all 0.2s ease;
}

.basket-item:last-child {
    margin-bottom: 0;
}

/* 茶摊区域 */
.tea-display {
    height: 200px;
    background-color: #e9e9e9;
    border-radius: 5px;
    padding: 10px;
    overflow-x: auto;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    white-space: nowrap;
}

.no-tea {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
}

.tea-item {
    display: inline-block;
    width: 100px;
    height: 160px;
    padding: 10px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    text-align: center;
    position: relative;
}

.tea-name {
    font-weight: bold;
    margin-bottom: 4px;
    font-size: 12px;
}

.tea-temp {
    font-size: 11px;
    margin-bottom: 4px;
}

/* 加料样式 */
.tea-toppings {
    font-size: 11px;
    margin-bottom: 4px;
    color: #7a7a7a;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 33px;
}

.hot-tea {
    color: #808080;
}

.cold-tea {
    color: #a9a9a9;
}

.tea-actions {
    position: absolute;
    bottom: 10px;
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 0 5px;
    margin-top: 10px;
}

.tea-action-btn {
    padding: 3px 6px;
    font-size: 11px;
    border: none;
    border-radius: 3px;
    background-color: #d9d9d9;
    color: #4a4a4a;
    cursor: pointer;
    width: 100%;
}

.tea-action-btn.serve-tea {
    background-color: #d9d9d9;
}

.tea-action-btn.serve-tea:active {
    background-color: #c0c0c0;
}

.tea-action-btn.add-topping {
    background-color: #d9d9d9;
}

.tea-action-btn.add-topping:active {
    background-color: #c0c0c0;
}

.toppings-display {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    padding: 8px 12px;
    background-color: #f9f9f9;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    justify-content: space-around;
    align-content: flex-start;
}

.topping-item {
    background-color: #e0e0e0;
    border: 1px solid #d0d0d0;
    border-radius: 20px;
    padding: 8px 12px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    flex: 0 1 calc(33.333% - 8px);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    min-width: 90px;
    max-width: 120px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    gap: 4px;
    margin-bottom: 4px;
}

.topping-item:hover {
    background-color: #d0d0d0;
    transform: translateY(-1px);
}

.topping-item:active {
    transform: translateY(0);
    background-color: #c0c0c0;
}

.topping-name {
    font-weight: bold;
    font-size: 11px;
    color: #4a4a4a;
    margin: 0;
    line-height: 1.2;
    position: relative;
    white-space: nowrap;
}

/* 小料图标 - 通过JavaScript动态添加 */
.topping-icon {
    display: inline-block;
    font-size: 16px;
    margin: 0;
    flex-shrink: 0;
}

.topping-count {
    font-size: 10px;
    color: #4a4a4a;
    background-color: rgba(255,255,255,0.9);
    border-radius: 10px;
    padding: 2px 6px;
    min-width: 24px;
    font-weight: bold;
    text-align: center;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    flex-shrink: 0;
}

/* 响应式调整 */
@media (max-width: 320px) {
    body {
        font-size: 13px;
    }

    .seed-grid, .item-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .topping-item {
        flex: 1 0 100%;
    }

    .game-content {
        max-height: calc(100vh - 120px);
    }

    /* 小屏幕配方网格调整 */
    .recipe-items {
        grid-template-columns: 1fr;
        gap: 8px;
    }
}

/* 添加中等尺寸手机的适配 */
@media (max-width: 480px) {
    .game-content {
        max-height: calc(100vh - 130px);
        -webkit-overflow-scrolling: touch;
    }

    .container {
        padding: 5px;
    }

    .plot {
        padding: 8px;
    }

    /* 中等屏幕配方网格调整 */
    .recipe-items {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 8px;
    }

    /* 购物车样式小屏幕优化 */
    .cart-preview {
        padding: 10px 14px;
        height: 260px;
        gap: 6px;
        position: absolute !important;
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        margin: 0 !important;
    }

    .cart-preview-button {
        padding: 12px 16px;
        font-size: 14px;
        min-height: 44px;
    }

    .cart-preview-items {
        min-height: 120px;
        max-height: 140px;
        padding: 6px;
    }

    .cart-preview-item {
        padding: 6px 8px;
        font-size: 11px;
    }

    .cart-preview-title {
        font-size: 14px;
    }

    .cart-preview-total > div:first-child {
        font-size: 11px;
    }

    .cart-preview-total > div:last-child {
        font-size: 14px;
    }

    .cart-popup-panel {
        width: 95%;
        max-height: 80vh;
    }

    .cart-popup-content {
        padding: 20px;
    }

    .cart-item {
        padding: 10px 6px;
    }

    .cart-item-name {
        font-size: 13px;
        margin-right: 8px;
    }

    .cart-item-price {
        margin: 0 8px;
        font-size: 12px;
    }

    .quantity-btn {
        width: 26px;
        height: 26px;
        font-size: 13px;
    }

    .quantity-value {
        width: 25px;
        margin: 0 6px;
        font-size: 12px;
    }

    .basket-cart-section {
        padding: 12px;
        margin-top: 15px;
    }

    .basket-cart-item {
        padding: 6px 10px;
        font-size: 12px;
    }

    .cart-popup-btn {
        padding: 12px 16px;
        font-size: 14px;
    }

    /* 商店面板小屏幕优化 */
    .shop-panel {
        width: 98%;
        height: 90vh;
        border-radius: 16px;
    }

    .shop-header {
        padding: 16px 20px;
    }

    .shop-header h2 {
        font-size: 18px;
    }

    .shop-tabs, .recipe-tabs, .game-tabs {
        margin: 0 16px 16px 16px;
    }

    .tab-btn, .game-tab {
        padding: 10px 12px;
        font-size: 13px;
    }

    .shop-items-container {
        padding: 0 16px 16px 16px;
        max-height: calc(100% - 340px);
    }

    .shop-section {
        padding: 16px;
        margin-bottom: 20px;
    }

    .shop-section-title {
        font-size: 15px;
        margin-bottom: 12px;
    }

    .seed-grid, .item-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 12px;
    }

    .seed-btn, .shop-item-btn {
        padding: 12px 8px;
        min-height: 60px;
        font-size: 12px;
    }

    .close-btn {
        width: 38px;
        height: 38px;
        font-size: 18px;
    }
}

/* 购物车列表样式 - 优化版 */
.cart-list {
    margin: 12px 0;
    max-height: 180px;
    overflow-y: auto;
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: 10px;
    padding: 8px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 6px rgba(0,0,0,0.08);
    -webkit-overflow-scrolling: touch;
}

.cart-item-preview {
    padding: 10px 12px;
    margin-bottom: 6px;
    background: white;
    border-radius: 8px;
    font-size: 13px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #f0f0f0;
    transition: all 0.2s ease;
}

.cart-item-preview:last-child {
    margin-bottom: 0;
}

.cart-item-preview:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    border-color: #dee2e6;
}

.cart-item-preview .item-name {
    font-weight: 500;
    color: #333;
    margin-right: 12px;
    flex: 1;
    word-wrap: break-word;
    line-height: 1.3;
}

.cart-item-preview .item-quantity {
    color: #6c757d;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 11px;
    margin: 0 8px;
    min-width: 25px;
    text-align: center;
}

.cart-item-preview .item-price {
    color: #7c4f3f;
    font-weight: 600;
    white-space: nowrap;
}



/* 农田和厨房集成布局 */
.farm-kitchen-layout {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 15px;
}

@media (min-width: 600px) {
    .farm-kitchen-layout {
        flex-direction: row;
    }

    .farm-section {
        flex: 3;
        margin-right: 10px;
    }

    .kitchen-section {
        flex: 2;
    }
}

.farm-section, .kitchen-section {
    background-color: #f9f9f9;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* 快速操作栏 */
.quick-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.quick-action-btn {
    flex: 1;
    padding: 10px;
    background-color: #a8a8a8;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
}

.quick-action-btn:active {
    background-color: #888888;
}

/* 统一农田并排显示 */
.plots-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.plot {
    flex: 0 0 calc(50% - 5px);
    background-color: #e9e9e9;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 0;
}

/* 统一按钮样式 */
.shop-corner-btn, .recipe-corner-btn, .serve-corner-btn, .recipe-btn {
    background-color: #f0f0f0;
    color: #666666;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 14px;
    cursor: pointer;
}

.shop-corner-btn:active, .recipe-corner-btn:active, .serve-corner-btn:active, .recipe-btn:active {
    background-color: #e0e0e0;
}

/* 右上角商店按钮样式 */
.shop-corner-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 10px;
    background-color: #f0f0f0;
    color: #666666;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 14px;
    cursor: pointer;
    z-index: 10;
    line-height: 1.2;
    height: 28px;
    display: flex;
    align-items: center;
}

.shop-corner-btn:active {
    background-color: #e0e0e0;
}

/* 左上角配方按钮样式 */
.recipe-corner-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 10px;
    background-color: #f0f0f0;
    color: #666666;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 14px;
    cursor: pointer;
    z-index: 10;
    line-height: 1.2;
    height: 28px;
    display: flex;
    align-items: center;
}

.recipe-corner-btn:active {
    background-color: #e0e0e0;
}

/* 服务顾客按钮样式 */
.serve-corner-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 5px; /* 在商店按钮左侧 */
    background-color: #888888;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 14px;
    cursor: pointer;
    z-index: 10;
    line-height: 1.2;
    height: 28px;
    display: flex;
    align-items: center;
}

.serve-corner-btn:active {
    background-color: #666666;
}

/* 菜单按钮样式 */
.menu-panel button, #buy-seed-farm, #water-farm, #fertilize-farm, #dig-out-farm {
    background-color: #888888;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    text-align: center;
}

/* 购物车空提示 */
.cart-empty-msg {
    text-align: center;
    color: #999;
    padding: 10px 0;
}

/* 种子项目样式 */
.seed-item {
    background-color: #f0f0f0 !important;
    border-radius: 4px;
    padding: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 库存物品样式 */
.inventory-item {
    background-color: #f0f0f0 !important;
    border-radius: 4px;
    padding: 6px;
}

.item-name {
    font-weight: bold;
    font-size: 12px;
}

.item-count {
    color: #666;
    font-size: 11px;
}

/* 种植双击提示 */
.seed-item:after {
    content: '';
    position: absolute;
    right: 8px;
    color: #4CAF50;
    font-size: 10px;
    opacity: 0.7;
}

/* 物品点击提示 */
.inventory-item:after {
    content: '';
    position: absolute;
    right: 8px;
    color: #2196F3;
    font-size: 10px;
    opacity: 0.7;
}

/* 高亮动画效果 */
@keyframes highlight {
    0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(76, 175, 80, 0); }
    100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
}

/* 可点击的农田元素 */
.clickable-stat {
    position: relative;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.plot-moisture.clickable-stat {
    background-color: rgba(33, 150, 243, 0.1);
}

.plot-moisture.clickable-stat:hover,
.plot-moisture.clickable-stat:active {
    background-color: rgba(33, 150, 243, 0.2);
}

.plot-fertility.clickable-stat {
    background-color: rgba(76, 175, 80, 0.1);
}

.plot-fertility.clickable-stat:hover,
.plot-fertility.clickable-stat:active {
    background-color: rgba(76, 175, 80, 0.2);
}

/* 提示图标 */
.clickable-stat::after {
    content: '↑';
    font-size: 10px;
    margin-left: 2px;
    color: #666;
}

/* 双击地块提示 */
.plot {
    position: relative;
}

.plot::before {
    content: '双击挖出';
    position: absolute;
    top: 4px;
    right: 4px;
    font-size: 9px;
    color: #999;
    opacity: 0.8;
    pointer-events: none;
}

/* 可收获提示 */
.plot-timer:empty:before {
    content: '';
}

.plot-timer:empty {
    position: relative;
}

.plot-timer:empty::after {
    content: '点击收获';
    color: #4CAF50;
    font-weight: bold;
    font-size: 11px;
}

/* 炉灶配方选择面板 */
.recipe-select-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    background-color: #f2f2f2;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    display: none; /* 确保面板默认隐藏 */
    overflow: hidden;
    flex-direction: column;
}

.recipe-select-header {
    padding: 10px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.recipe-select-content {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    max-height: 60vh;
    -webkit-overflow-scrolling: touch;
}

.recipe-select-list {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 15px;
}

.recipe-item-select {
    padding: 12px;
    margin-bottom: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.recipe-item-select.selected {
    background-color: #e8f5e9;
    border-left: 3px solid #4CAF50;
}

.recipe-item-select:hover {
    background-color: #f0f0f0;
}

.recipe-select-description {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
}

#selected-recipe-name {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 6px;
}

#selected-recipe-ingredients {
    font-size: 12px;
    color: #666;
}

.ingredient-available {
    color: #4CAF50;
}

.ingredient-missing {
    color: #F44336;
}

.recipe-select-buttons {
    display: flex;
    gap: 10px;
}

.recipe-select-buttons button {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
}

#make-recipe-btn {
    background-color: #4CAF50;
    color: white;
}

#make-recipe-btn:disabled {
    background-color: #ccc;
    color: #666;
}

#cancel-recipe-btn {
    background-color: #f0f0f0;
}

/* 顾客通知样式 */
.customer-notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(92, 184, 92, 0.9);
    color: white;
    padding: 15px 20px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    z-index: 2000;
    max-width: 80%;
    text-align: center;
    animation: slideDown 0.3s ease-out;
}

.customer-notification.fadeout {
    animation: fadeOut 0.5s ease-out forwards;
}

@keyframes slideDown {
    0% { transform: translate(-50%, -50px); opacity: 0; }
    100% { transform: translate(-50%, 0); opacity: 1; }
}

@keyframes fadeOut {
    0% { opacity: 1; }
    100% { opacity: 0; }
}

/* 删除未使用的种子信息样式 */
.seed-info {
    display: none;
}

/* 配方列表样式 */
.recipe-items {
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
}

#tea-info-panel {
    width: 100%;
    min-height: 40px;
    margin-bottom: 10px;
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tea-info-msg {
    color: #2e7d32;
    font-size: 13px;
    font-weight: bold;
    animation: fadeInTeaInfo 0.5s;
    transition: opacity 0.5s;
    white-space: pre-line; /* 支持显示换行符 */
}

@keyframes fadeInTeaInfo {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 集卡面板样式 */
.collection-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.collection-header {
    padding: 16px;
    background: #f8f8f8;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.collection-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.collection-content {
    padding: 16px;
    overflow-y: auto;
    flex: 1;
}

.collection-card {
    background: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
    transition: transform 0.2s;
}

.collection-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-name {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    margin-bottom: 6px;
}

.card-count {
    font-size: 12px;
    color: #666;
    margin-bottom: 3px;
}

.card-visit {
    font-size: 10px;
    color: #999;
}

.no-cards {
    text-align: center;
    color: #999;
    padding: 32px 0;
}

/* 植物图标样式 */
.plot-icon {
    margin-left: 5px;
    font-size: 16px;
}

/* 炉灶和案板区域卡片样式 */
.stoves-container, .processing-container {
    background-color: #f9f9f9;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 15px;
}

/* 特别为消息记录滑块设置更紧凑的高度 */
.swiper-slide.message-log {
    height: auto;
    max-height: 120px;
}

/* 茶摊区域卡片样式 */
.tea-container {
    background-color: #f9f9f9;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 15px;
    /* 添加可滚动特性，确保长内容在小屏幕上可滚动查看 */
    max-height: 80vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

/* 小料标题区域样式 */
.tea-container .toppings-header {
    background-color: transparent;
    box-shadow: none;
    margin-top: 10px;
    margin-bottom: 0;
    padding: 0;
    /* 改为正常布局，不再固定顶部 */
    position: static;
    background-color: #f9f9f9;
}

.toppings-header .toppings-display {
    background-color: #e9e9e9;
    border-radius: 8px;
    padding: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    min-height: 60px;
}

/* 针对小屏幕调整茶摊区域高度 */
@media (max-width: 480px) {
    .tea-container {
        max-height: 60vh;
    }

    .tea-display {
        padding: 5px;
    }

    .tea-item {
        padding: 8px;
        margin-bottom: 6px;
    }

    .topping-item {
        flex: 0 1 calc(50% - 6px);
        padding: 6px 8px;
        min-width: 80px;
        max-width: 100px;
        gap: 3px;
    }

    .toppings-display {
        gap: 4px;
        padding: 6px 8px;
    }
}

/* 简洁的游戏状态显示 */
.game-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 10px;
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 8px;
    font-size: 12px;
}

.status-info {
    display: flex;
    gap: 6px;
    align-items: center;
}

.season-badge, .weather-badge, .day-badge {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: normal;
    text-align: center;
    border: 1px solid #ccc;
    background: white;
    color: #333;
}

.status-buttons {
    display: flex;
    gap: 4px;
    align-items: center;
}

.status-btn {
    width: 28px;
    height: 28px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    background: white;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-btn:hover {
    background: #f0f0f0;
}

.status-btn:active {
    background: #e0e0e0;
}

/* 购物车按钮特殊样式 */
.status-btn.cart-btn {
    background: linear-gradient(135deg, #ff9800, #f57c00);
    color: white;
    border: 1px solid #f57c00;
    box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.status-btn.cart-btn:hover {
    background: linear-gradient(135deg, #f57c00, #ff9800);
    box-shadow: 0 4px 12px rgba(255, 152, 0, 0.4);
    transform: translateY(-1px);
}

.status-btn.cart-btn:active {
    background: linear-gradient(135deg, #e65100, #f57c00);
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(255, 152, 0, 0.5);
}

/* 确保信息面板中的所有文字都较小 */
.weather-season .customer-info-row,
.weather-season .info-label,
.weather-season #customer-name,
.weather-season #customer-tea,
.weather-season #customer-toppings,
.weather-season #patience-timer {
    font-size: 14px !important; /* 从13px调整为14px */
}

/* 测试模式样式 */
.test-mode-panel {
    position: fixed;
    top: 10%;
    left: 5%;
    width: 90%;
    max-width: 450px; /* 增大最大宽度以容纳更多内容 */
    max-height: 85%; /* 增加高度 */
    background-color: #fff; /* 白色背景 */
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    z-index: 1001;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: none;
}

.test-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    cursor: move;
}

.test-title {
    font-size: 16px;
    font-weight: bold;
}

.test-content {
    padding: 15px;
    overflow-y: auto;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* 测试区块样式 */
.test-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    border: 1px solid #e9ecef;
}

.test-section-title {
    font-size: 14px;
    font-weight: bold;
    color: #495057;
    margin-bottom: 10px;
    border-left: 3px solid #4CAF50;
    padding-left: 8px;
}

.test-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
}

.test-controls.special-customers {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
}

/* 测试按钮样式 */
.test-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    background-color: #007bff;
    color: white;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.test-btn:hover {
    background-color: #0056b3;
    transform: translateY(-1px);
}

.test-btn:active {
    transform: translateY(0);
}

.test-btn.warning {
    background-color: #ffc107;
    color: #212529;
}

.test-btn.warning:hover {
    background-color: #e0a800;
}

.test-btn.danger {
    background-color: #dc3545;
}

.test-btn.danger:hover {
    background-color: #c82333;
}

.test-btn.special {
    background-color: #6f42c1;
}

.test-btn.special:hover {
    background-color: #5a32a3;
}

/* 测试状态区域 */
.test-status {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    padding: 12px;
    border: 1px solid #dee2e6;
    margin-top: auto;
}

.test-status-title {
    font-size: 13px;
    font-weight: bold;
    color: #495057;
    margin-bottom: 8px;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 4px;
}

.test-status-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.test-status-content div {
    font-size: 11px;
    color: #6c757d;
}

.test-status-content span {
    font-weight: bold;
    color: #495057;
}

/* 移动端响应式样式 */
@media (max-width: 768px) {
    .test-mode-panel {
        top: 5%;
        left: 2%;
        width: 96%;
        max-width: none;
        max-height: 90%;
    }

    .test-header {
        padding: 10px 12px;
    }

    .test-title {
        font-size: 14px;
    }

    .test-content {
        padding: 12px;
        gap: 12px;
    }

    .test-section {
        padding: 10px;
    }

    .test-section-title {
        font-size: 13px;
    }

    .test-controls {
        grid-template-columns: 1fr;
        gap: 6px;
    }

    .test-controls.special-customers {
        grid-template-columns: 1fr;
    }

    .test-btn {
        padding: 10px 12px;
        font-size: 13px;
        white-space: normal;
        text-overflow: unset;
        overflow: visible;
        min-height: 40px;
    }

    .test-status {
        padding: 10px;
    }

    .test-status-title {
        font-size: 12px;
    }

    .test-status-content div {
        font-size: 12px;
    }
}

/* 测试模式指示器 */
.test-mode-indicator {
    position: fixed;
    top: 60px;
    right: 10px;
    background-color: #ff9800;
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.8rem;
    z-index: 1000;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* 菜单暂停按钮样式 */
.menu-pause-btn {
    position: relative;
}

.menu-pause-btn.active::before {
    content: "继续游戏";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #4CAF50;
    color: white;
}

/* 暂停遮罩 */
.pause-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.pause-overlay.active {
    display: flex;
}

.resume-btn {
    margin-top: 20px;
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 18px;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

/* 解锁进度面板样式 */
.unlock-progress-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    z-index: 2000;
    overflow: hidden;
    animation: fadeIn 0.3s ease-out;
}

.unlock-header {
    padding: 15px;
    background: #7c4f3f;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.unlock-title {
    font-size: 18px;
    font-weight: bold;
    margin: 0;
}

.unlock-content {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.progress-info {
    background: #f8f8f8;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #7c4f3f;
}

.current-progress {
    font-size: 16px;
    font-weight: bold;
    color: #7c4f3f;
    margin-bottom: 8px;
}

.next-unlock {
    font-size: 14px;
    color: #666;
}

.unlock-section-title {
    font-size: 16px;
    font-weight: bold;
    color: #7c4f3f;
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 1px solid #e0e0e0;
}

.unlock-recipes {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.unlock-recipe-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #f9f9f9;
    border-radius: 6px;
    border-left: 3px solid #ccc;
    transition: all 0.2s;
}

.unlock-recipe-item.unlocked {
    background: #e8f5e8;
    border-left-color: #4CAF50;
}

.unlock-recipe-item.next-unlock {
    background: #fff3cd;
    border-left-color: #ffc107;
    animation: pulse 2s infinite;
}

.unlock-recipe-name {
    font-weight: bold;
    color: #333;
    margin-bottom: 2px;
}

.unlock-recipe-requirement {
    font-size: 12px;
    color: #666;
}

.unlock-recipe-status {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    text-align: center;
    min-width: 60px;
}

.unlock-recipe-status.unlocked {
    background: #4CAF50;
    color: white;
}

.unlock-recipe-status.locked {
    background: #ccc;
    color: #666;
}

.unlock-recipe-status.next {
    background: #ffc107;
    color: #333;
    font-weight: bold;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

/* 篮子查看面板样式 */
.basket-view-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    z-index: 2000;
    overflow: hidden;
}

.basket-view-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #7c4f3f;
    color: white;
}

.basket-view-title {
    font-size: 16px;
    font-weight: 600;
}

.basket-view-content {
    padding: 20px;
    max-height: calc(80vh - 60px);
    overflow-y: auto;
}

.basket-section {
    margin-bottom: 20px;
}

.basket-section:last-child {
    margin-bottom: 0;
}

.basket-section-title {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 2px solid #ecf0f1;
    display: flex;
    align-items: center;
    gap: 5px;
}

.basket-items {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.basket-item-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.basket-item-card:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.basket-item-icon {
    font-size: 16px;
    flex-shrink: 0;
}

.basket-item-name {
    font-size: 12px;
    font-weight: 500;
    color: #2c3e50;
    flex: 1;
}

.basket-item-count {
    font-size: 12px;
    color: #7f8c8d;
    font-weight: 600;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 20px;
    text-align: center;
}

.basket-empty {
    text-align: center;
    color: #95a5a6;
    font-style: italic;
    padding: 20px;
    font-size: 12px;
}

/* 购物车弹出窗口样式 - 优化版 */
.cart-popup-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 92%;
    max-width: 420px;
    max-height: 75vh;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 50px rgba(0,0,0,0.25);
    z-index: 2000;
    overflow: hidden;
    border: 1px solid #e0e0e0;
    animation: popupAppear 0.3s ease-out;
}

@keyframes popupAppear {
    from {
        opacity: 0;
        transform: translate(-50%, -60%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.cart-popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px 24px;
    background: linear-gradient(135deg, #7c4f3f, #8b5a3c);
    color: white;
    box-shadow: 0 2px 8px rgba(124, 79, 63, 0.2);
}

.cart-popup-title {
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.cart-popup-content {
    padding: 24px;
    max-height: calc(75vh - 80px);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.cart-popup-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    margin-bottom: 20px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

.cart-popup-count, .cart-popup-total {
    font-size: 15px;
    font-weight: 600;
    color: #2c3e50;
}

.cart-popup-total {
    color: #7c4f3f;
    font-weight: 700;
    font-size: 16px;
}

.cart-popup-items {
    max-height: 220px;
    overflow-y: auto;
    margin-bottom: 20px;
    background: #fafafa;
    border-radius: 10px;
    padding: 12px;
    border: 1px solid #e9ecef;
}

.cart-empty-message {
    text-align: center;
    color: #95a5a6;
    font-style: italic;
    padding: 40px 20px;
    font-size: 15px;
}

.cart-popup-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 8px;
    margin-bottom: 8px;
    background: white;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
    transition: all 0.2s ease;
}

.cart-popup-item:last-child {
    margin-bottom: 0;
}

.cart-popup-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-color: #dee2e6;
    transform: translateY(-1px);
}

.cart-popup-item-name {
    font-size: 14px;
    color: #2c3e50;
    flex: 1;
    font-weight: 500;
    margin-right: 12px;
    word-wrap: break-word;
    line-height: 1.3;
}

.cart-popup-item-quantity {
    font-size: 13px;
    color: #6c757d;
    margin: 0 12px;
    background: #e9ecef;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 600;
    min-width: 35px;
    text-align: center;
}

.cart-popup-item-price {
    font-size: 14px;
    color: #7c4f3f;
    font-weight: 600;
    white-space: nowrap;
}

.cart-popup-actions {
    display: flex;
    gap: 12px;
    padding-top: 20px;
    border-top: 2px solid #e9ecef;
}

.cart-popup-btn {
    flex: 1;
    padding: 14px 20px;
    border: none;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.cart-popup-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.cart-popup-btn:hover::before {
    width: 200px;
    height: 200px;
}

.cart-popup-btn.clear-btn {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    color: #6c757d;
    border: 1px solid #dee2e6;
}

.cart-popup-btn.clear-btn:hover {
    background: linear-gradient(135deg, #e9ecef, #dee2e6);
    color: #495057;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.cart-popup-btn.checkout-btn {
    background: linear-gradient(135deg, #7c4f3f, #8b5a3c);
    color: white;
}

.cart-popup-btn.checkout-btn:hover {
    background: linear-gradient(135deg, #6a4334, #7a4b38);
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(124, 79, 63, 0.4);
}

.cart-popup-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.cart-popup-btn:disabled::before {
    display: none;
}