# 茶铺游戏界面改版计划

## 改版目标
- 简化用户操作，避免上下滚动寻找功能
- 优化界面布局，提高用户体验
- 保持所有现有功能不变，只进行界面重构

## 当前界面问题
1. 茶水摊小篮子显示占用空间，用户需要滚动查看其他功能
2. 商店按钮位置不够显眼
3. 种植和厨房功能混合在一个选项卡中，布局拥挤
4. 种子选择需要打开单独的商店面板，操作路径较长

## 改版方案

### 1. 选项卡重构
**原有结构：**
- 种植与厨房 (包含：田地 + 小篮子 + 炉灶 + 案板)
- 茶摊 (包含：茶饮展示 + 小料区)

**新结构：**
- 种植 (只包含：田地)
- 厨房 (包含：炉灶 + 案板) 
- 茶摊 (保持不变：茶饮展示 + 小料区)

### 2. 功能优化

#### 2.1 移除茶水摊小篮子显示
- 从种植选项卡中移除小篮子显示区域
- 小篮子功能通过点击田地物品区域来访问

#### 2.2 田地交互优化
- 在田地的"物品:"字样后面的区域添加点击热区
- 点击后弹出小篮子选择窗口，显示可种植的种子
- 窗口内容：
  - 显示所有种子类型
  - 有库存的种子正常显示
  - 无库存的种子显示为灰色
  - 点击灰色种子提示购买

#### 2.3 商店按钮重新定位
- 将商店按钮从小篮子区域移动到顶部菜单栏
- 或者移动到种植选项卡的明显位置

#### 2.4 小篮子选择窗口设计
```
┌─────────────────────────────┐
│ 选择种植物品           ×    │
├─────────────────────────────┤
│ 库存种子：                  │
│ [五味子] [乌梅] [山楂]      │
│ [陈皮] [甘草] [桂花]        │
│                             │
│ 缺货种子（点击购买）：       │
│ [大麦↗] [菊花↗] [金银花↗]   │
│                             │
│ [取消] [确认种植]            │
└─────────────────────────────┘
```

## 实施步骤

### 步骤1：HTML结构调整
1. 修改游戏选项卡，从2个改为3个
2. 重新分配各功能区域到对应选项卡
3. 移除小篮子显示区域
4. 为田地物品区域添加点击热区

### 步骤2：创建小篮子选择窗口
1. 设计弹窗HTML结构
2. 添加种子选择界面
3. 实现库存/缺货状态显示
4. 添加购买提示功能

### 步骤3：CSS样式更新
1. 调整选项卡布局样式
2. 优化各功能区域在新选项卡中的布局
3. 设计小篮子选择窗口样式
4. 更新商店按钮位置样式

### 步骤4：JavaScript逻辑调整
1. 更新选项卡切换逻辑
2. 实现田地物品区域点击事件
3. 实现小篮子选择窗口的显示/隐藏
4. 实现库存检查和购买提示
5. 保持所有现有游戏逻辑不变

### 步骤5：功能测试
1. 测试选项卡切换功能
2. 测试田地种植流程
3. 测试小篮子选择和购买流程
4. 确保所有原有功能正常运行

## 预期效果
- 用户无需滚动即可访问所有主要功能
- 种植操作更加直观便捷
- 界面布局更加清晰有序
- 保持游戏的所有现有功能和体验

## 风险控制
- 在修改过程中保持所有游戏逻辑变量和函数不变
- 分步骤实施，每步完成后进行功能验证
- 保留原有事件处理逻辑，只调整触发方式

## 时间安排
- 步骤1-2：2小时（结构调整）
- 步骤3：1小时（样式调整）
- 步骤4：2小时（逻辑调整）
- 步骤5：1小时（测试验证）
- 总计：约6小时完成改版 