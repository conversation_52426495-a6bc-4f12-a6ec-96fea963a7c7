# 🍵 可爱茶铺 - 完整游戏系统规则

## 📋 目录
1. [天气系统规则](#天气系统规则)
2. [农场种植规则](#农场种植规则)
3. [厨房制茶规则](#厨房制茶规则)
4. [顾客系统规则](#顾客系统规则)
5. [配方解锁规则](#配方解锁规则)
6. [小料加工规则](#小料加工规则)
7. [经济系统规则](#经济系统规则)
8. [时间系统规则](#时间系统规则)

---

## 🌤️ 天气系统规则

### 基础设置
- **季节循环**: 春天 → 夏天 → 秋天 → 冬天 → 春天
- **天气类型**: 晴天、刮风、下雨、下雪、阴天
- **天气持续时间**: 20秒 (20000毫秒)
- **每季节天数**: 10天

### 天气限制规则
```javascript
// 季节天气限制
- 冬天不能下雨
- 非冬天不能下雪
- 不能连续相同天气
```

### 天气效果
```javascript
// 对农田的影响
下雨: 湿度 +20
刮风: 湿度 -10
下雪: 湿度 +15, 肥力 +10 (仅冬天)
```

---

## 🌱 农场种植规则

### 田地基础设置
- **田地数量**: 4块
- **生长阶段**: ["长芽", "大叶子", "开花", "成熟"]
- **每阶段时长**: 15秒 (15000毫秒)
- **初始湿度**: 50%
- **初始肥力**: 50%

### 生长条件
```javascript
// 最低要求
最低湿度: 10%
最低肥力: 20%

// 消耗速率
湿度消耗: 10% (随机触发)
肥力消耗: 5% (随机触发)
```

### 种子配置
```javascript
// 基础种子 (价格1铜板, 30秒生长)
基础材料: ["五味子", "乌梅", "山楂", "陈皮", "甘草", ...]

// 特殊种子配置
"桑叶": { price: 2, growTime: 45000 },      // 45秒
"杭白菊": { price: 2, growTime: 50000 },    // 50秒
"水蜜桃": { price: 3, growTime: 60000 },    // 60秒
"黄芪": { price: 3, growTime: 55000 },      // 55秒
"白茅根": { price: 2, growTime: 40000 },    // 40秒
"马蹄": { price: 2, growTime: 45000 },      // 45秒
"糯米": { price: 2, growTime: 50000 },      // 50秒
"米": { price: 1, growTime: 40000 }         // 40秒
```

### 农田操作成本
```javascript
浇水: 免费 (湿度 +30)
施肥: 2铜板 (肥力 +20)
```

---

## 🍳 厨房制茶规则

### 炉灶设置
- **炉灶数量**: 2个
- **制茶时间**: 20秒 (20000毫秒)
- **状态**: empty → boiling → done

### 茶饮配方
```javascript
// 基础配方 (默认解锁)
"五味子饮": ["五味子", "乌梅", "山楂"]
"柠檬茶": ["柠檬", "蜂蜜"]

// 特殊顾客解锁配方
"洛神玫瑰饮": ["洛神花", "玫瑰花", "山楂"]        // 凌小路解锁
"桂圆红枣茶": ["桂圆", "红枣", "枸杞"]            // 花花解锁
"焦香大麦茶": ["大麦"]                           // 江飞飞解锁
"三花决明茶": ["菊花", "金银花", "决明子", "枸杞"] // 江三解锁
"薄荷甘草凉茶": ["薄荷", "甘草"]                 // 江四解锁
"陈皮姜米茶": ["陈皮", "生姜"]                   // 池云旗解锁
"冬瓜荷叶饮": ["冬瓜", "荷叶", "薏米"]            // 江潮解锁
"古法酸梅汤": ["乌梅", "山楂", "陈皮", "甘草", "桂花"] // 池惊暮解锁
"小吊梨汤": ["雪花梨", "银耳", "话梅", "枸杞"]    // 江敕封解锁

// 人数解锁配方
"桑菊润燥茶": ["桑叶", "杭白菊"]          // 30人解锁
"桂花酒酿饮": ["桂花", "酒酿"]            // 60人解锁
"蜜桃乌龙冷萃": ["水蜜桃", "乌龙茶包"]     // 90人解锁
"黄芪枸杞茶": ["黄芪", "枸杞"]            // 120人解锁
"竹蔗茅根马蹄水": ["甘蔗", "白茅根", "马蹄"] // 150人解锁
```

### 茶饮温度系统
```javascript
制作完成: 热茶 (hot)
冷却时间: 20秒后变为冰茶 (cold)
```

---

## 👑 顾客系统规则

### 顾客生成
```javascript
// 生成概率
检查间隔: 5秒
生成概率: 20% (无顾客时提升到50%)

// 顾客类型
VIP顾客概率: 30%
普通顾客概率: 70%
```

### VIP顾客列表
```javascript
VIP顾客名单: [
    "池惊暮", "凌小路", "江飞飞", "江三", "江四",
    "池云旗", "江潮", "江敕封", "花花", "姬别情",
    "池九信", "狸怒"
]
```

### 耐心系统
```javascript
普通顾客耐心: 120秒 (120000毫秒)
VIP顾客耐心: 240秒 (240000毫秒)
```

### 小料需求
```javascript
// 随机选择0-2个小料
小料选择范围: 所有可用小料
选择数量: 0-2个 (随机)
```

---

## 🔓 配方解锁规则

### 特殊顾客解锁规则
```javascript
recipeUnlockRules: {
    "洛神玫瑰饮": {
        customer: "凌小路",
        visitsRequired: 1,
        chance: 1.0,
        guaranteedOnVisit: 1
    },
    "桂圆红枣茶": {
        customer: "花花",
        visitsRequired: 1,
        chance: 1.0,
        guaranteedOnVisit: 1
    },
    "焦香大麦茶": {
        customer: "江飞飞",
        visitsRequired: 2,
        chance: 1.0,
        guaranteedOnVisit: 2
    },
    "三花决明茶": {
        customer: "江三",
        visitsRequired: 2,
        chance: 1.0,
        guaranteedOnVisit: 2
    },
    "薄荷甘草凉茶": {
        customer: "江四",
        visitsRequired: 2,
        chance: 1.0,
        guaranteedOnVisit: 2
    },
    "陈皮姜米茶": {
        customer: "池云旗",
        visitsRequired: 2,
        chance: 0.5,
        guaranteedOnVisit: 3
    },
    "冬瓜荷叶饮": {
        customer: "江潮",
        visitsRequired: 3,
        chance: 0.6,
        guaranteedOnVisit: 4
    },
    "古法酸梅汤": {
        customer: "池惊暮",
        visitsRequired: 2,
        chance: 0.3,
        guaranteedOnVisit: 3
    },
    "小吊梨汤": {
        customer: "江敕封",
        visitsRequired: 3,
        chance: 0.4,
        guaranteedOnVisit: 5
    }
}
```

### 🎭 特殊顾客解锁故事

#### 1. 洛神玫瑰饮 - 凌小路解锁
**故事标题**: "朱砂"
**故事内容**: 凌小路袖中藏着一盏温热的洛神玫瑰饮。'疏肝解郁的，好好学学，飞飞来了就做给他。跟他说就说养颜的茶方子'挑眉笑时，眼底却映着刀光，袍角还沾着血。
**功效**: 疏肝解郁，美白养颜，活血调经，适合女子日常饮用。

#### 2. 桂圆红枣茶 - 花花解锁
**故事标题**: "无归"
**故事内容**: 花花去凌雪坟前扫墓，手里拿着他最喜欢她给他做的茶。只是这一次只能自己做了。'自己给自己作茶怎么行，这方子给你们，以后我就来这里喝吧'
**功效**: 补血益气，安神养心，滋阴润燥，适合体弱或熬夜者饮用。

#### 3. 焦香大麦茶 - 江飞飞解锁
**故事标题**: "雪夜"
**故事内容**: 长安冬夜，江飞飞蜷在凌雪阁的屋檐上，指尖冻得发僵。江三翻上屋顶，扔来一壶滚烫的大麦茶：'怂样，喝两口。'茶雾氤氲里，他忽然想起幼时第一次握刀，也是这焦苦的甜香压住了颤抖。
**功效**: 暖胃消食，缓解焦虑，安定心神，适合秋冬饮用。

#### 4. 三花决明茶 - 江三解锁
**故事标题**: "夜狩"
**故事内容**: 江四执刀归来，见江三伏案瞌睡，手边一盏凉透的三花决明茶。他轻叹，将外袍披上兄长肩头——却不知昨夜自己任务单上那三个名字，早已被江三的血刃划去。茶渣沉底，如未愈的旧伤。
**功效**: 清肝明目，清热解毒，缓解眼疲劳，适合长期伏案或夜视者饮用。

#### 5. 薄荷甘草凉茶 - 江四解锁
**故事标题**: "三哥"
**故事内容**: 江四给江三泡的茶，清清凉凉的，他那么爱出汗，肯定喜欢。茶叶刚放下，就听到三哥在院子里训练的刀声，他悄悄探头看了一眼，决定加多一片薄荷叶。
**功效**: 清热解暑，润喉止咳，提神醒脑，适合夏季饮用。

#### 6. 陈皮姜米茶 - 池云旗解锁
**故事标题**: "师徒"
**故事内容**: 池云旗心疼那小家伙，以前也不懂自己照顾自己，这茶是她专门给他找医师抄的方子。'别总吃那些乱七八糟的东西，胃疼了可别来找师父'虽然嘴上这么说，她还是悄悄在茶里多加了一片陈皮。
**功效**: 健脾和胃，理气化痰，温中散寒，适合消化不良或胃寒者饮用。

#### 7. 冬瓜荷叶饮 - 江潮解锁
**故事标题**: "师徒2"
**故事内容**: 江潮给师父弄的消暑茶，荷叶是自己趴在池塘边采的，冬瓜也是自己种的。'师父，您尝尝，我按照您说的方法做的'他小心翼翼地端着茶，生怕师父不喜欢，却不知道池云旗早已欣慰地笑了。
**功效**: 清热利湿，消肿减脂，美容养颜，适合夏季消暑或减肥者饮用。

#### 8. 古法酸梅汤 - 池惊暮解锁
**故事标题**: "梅香"
**故事内容**: 长安暑夜，池惊暮执剑伏于屋脊。目标出现时，她正饮尽最后一滴酸梅汤。瓷碗坠地碎响混着喉骨断裂声，梅妃教的小方子——杀人时唇齿间该留着甜味，才不苦。
**功效**: 生津止渴，消暑解腻，健脾开胃，缓解燥热，唐代已是宫廷消暑佳饮。

#### 9. 小吊梨汤 - 江敕封解锁
**故事标题**: "琴心"
**故事内容**: 江敕封抚琴时总爱在身边放一盏小吊梨汤，琴声悠扬，茶香袅袅。他说琴如人生，需要慢慢调教；茶如心境，需要细细品味。一曲终了，一盏茶尽，都是这世间最温柔的时光。
**功效**: 润肺止咳，清热降火，滋阴美容，宫廷传统滋补佳品。

### 人数解锁规则
```javascript
recipeUnlockRequirements: {
    "桑菊润燥茶": 30,      // 服务30位顾客
    "桂花酒酿饮": 60,      // 服务60位顾客
    "蜜桃乌龙冷萃": 90,    // 服务90位顾客
    "黄芪枸杞茶": 120,     // 服务120位顾客
    "竹蔗茅根马蹄水": 150  // 服务150位顾客
}
```

---

## 🍯 小料加工规则

### 初始小料库存
```javascript
初始小料: {
    "红糖": 5, "薄荷叶": 5, "姜丝": 5, "柚子丝": 5,
    "银耳丝": 5, "柠檬片": 5, "蜂蜜": 5
}

需加工小料: {
    "冰糖": 0, "乌龙茶包": 0, "干桂花": 0,
    "小圆子": 0, "酒酿": 0, "水蜜桃果肉": 0, "黄芪片": 0
}
```

### 加工配方 (1→3产出)
```javascript
processingRecipes: {
    '红糖': { ingredients: ['甘蔗'], time: 10000, output: 3 },
    '薄荷叶': { ingredients: ['薄荷'], time: 10000, output: 3 },
    '姜丝': { ingredients: ['生姜'], time: 10000, output: 3 },
    '柚子丝': { ingredients: ['柚子'], time: 10000, output: 3 },
    '银耳丝': { ingredients: ['银耳'], time: 15000, output: 3 },
    '柠檬片': { ingredients: ['柠檬'], time: 10000, output: 3 },
    '水蜜桃果肉': { ingredients: ['水蜜桃'], time: 12000, output: 3 },
    '黄芪片': { ingredients: ['黄芪'], time: 12000, output: 3 },
    '干桂花': { ingredients: ['桂花'], time: 10000, output: 3 },
    '小圆子': { ingredients: ['糯米'], time: 15000, output: 3 },
    '酒酿': { ingredients: ['米'], time: 18000, output: 3 }
}
```

---

## 💰 经济系统规则

### 收入计算
```javascript
// 基础收入
基础价格: 10铜板

// 奖励加成
小料奖励: 每个小料 +2铜板
VIP奖励: VIP顾客 +5铜板
温度奖励: 季节匹配温度 +3铜板
  - 夏天冰茶 +3铜板
  - 冬天热茶 +3铜板
```

### 商店价格
```javascript
shopItems: {
    '蜂蜜': { price: 3 },
    '银耳': { price: 3 },
    '红糖': { price: 2 },
    '薄荷叶': { price: 2 },
    '冰糖': { price: 3 },
    '乌龙茶包': { price: 4 }
}

// 种子价格
基础种子: 1铜板
特殊种子: 2-3铜板 (见种子配置)
```

### 操作成本
```javascript
施肥: 2铜板
浇水: 免费
```

---

## ⏰ 时间系统规则

### 游戏循环
```javascript
主循环: 每1秒执行一次
  - 更新天气和季节
  - 更新植物生长
  - 更新炉灶状态
  - 更新顾客状态
  - 更新茶饮温度

快速循环: 每100毫秒执行一次
  - 更新进度条显示
  - 更新计时器显示
```

### 自动保存
```javascript
自动保存间隔: 30秒
页面卸载时: 自动保存
页面隐藏时: 暂停游戏
页面显示时: 恢复游戏
```

---

## 📝 总结

这套规则系统确保了游戏的平衡性和趣味性：

1. **渐进式解锁**: 通过服务特殊顾客和达成人数目标解锁新配方
2. **资源管理**: 平衡种植、加工、制茶和服务的资源分配
3. **季节性玩法**: 天气影响农田，温度影响收入
4. **收集要素**: VIP顾客系统和配方收集
5. **经济平衡**: 合理的收入和支出比例

请确认这些规则是否符合您的预期，我将据此完善现有系统。
