/* 🍵 可爱茶铺 - 完整游戏样式 */

:root {
    /* 主要颜色：绿色系 */
    --primary-green: #4CAF50;
    --light-green: #81C784;
    --soft-green: #E8F5E8;
    
    /* 辅助颜色：白色 */
    --pure-white: #FFFFFF;
    --soft-white: #F8F9FA;
    
    /* 点缀颜色：暖黄色 */
    --accent-yellow: #FFD54F;
    --light-yellow: #FFF9C4;
    
    /* 文字颜色 */
    --text-dark: #2E7D32;
    --text-medium: #388E3C;
    --text-light: #66BB6A;
    
    /* 阴影 */
    --shadow-soft: 0 4px 12px rgba(76, 175, 80, 0.15);
    --shadow-medium: 0 6px 20px rgba(76, 175, 80, 0.2);
    --shadow-strong: 0 8px 25px rgba(76, 175, 80, 0.25);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI Emoji', sans-serif;
    background: linear-gradient(135deg, var(--soft-green) 0%, var(--pure-white) 100%);
    color: var(--text-dark);
    line-height: 1.6;
    min-height: 100vh;
    padding: 16px;
    overflow-x: hidden;
}

/* 🌟 可爱的动画效果 */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes wiggle {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-3deg); }
    75% { transform: rotate(3deg); }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 15px rgba(255, 213, 79, 0.5);
    }
    50% {
        box-shadow: 0 0 25px rgba(255, 213, 79, 0.8);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes sparkle {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.2);
    }
}

/* 🏠 顶部区域 */
.cute-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--pure-white);
    border-radius: 24px;
    padding: 16px 20px;
    margin-bottom: 16px;
    box-shadow: var(--shadow-soft);
    position: relative;
    overflow: hidden;
}

.cute-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-green), var(--light-green), var(--accent-yellow));
}

.coins-bubble {
    display: flex;
    align-items: center;
    background: var(--light-yellow);
    border-radius: 20px;
    padding: 8px 16px;
    animation: pulse 2s infinite;
}

.coin-icon {
    font-size: 20px;
    margin-right: 8px;
    animation: wiggle 3s infinite;
}

.coin-count {
    font-weight: bold;
    color: var(--text-dark);
    font-size: 16px;
}

.title-container {
    text-align: center;
    flex: 1;
}

.cute-title {
    font-size: 24px;
    color: var(--primary-green);
    margin: 0;
    text-shadow: 2px 2px 4px rgba(76, 175, 80, 0.2);
}

.subtitle {
    font-size: 12px;
    color: var(--text-medium);
    margin-top: 2px;
}

.menu-bubble {
    background: var(--primary-green);
    border: none;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-medium);
    transition: all 0.3s ease;
    cursor: pointer;
}

.menu-bubble:hover {
    transform: scale(1.1);
    background: var(--light-green);
}

.menu-icon {
    color: white;
    font-size: 18px;
    font-weight: bold;
}

/* 💬 信息卡片区域 */
.info-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 16px;
}

.info-card {
    background: var(--pure-white);
    border-radius: 20px;
    padding: 16px;
    box-shadow: var(--shadow-soft);
    transition: all 0.3s ease;
}

.info-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.weather-card .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.weather-icon {
    font-size: 24px;
    margin-right: 8px;
    animation: bounce 2s infinite;
}

.season-text {
    font-size: 14px;
    color: var(--text-medium);
    font-weight: 500;
}

.day-info {
    font-size: 16px;
    color: var(--text-dark);
    font-weight: bold;
}

.day-number {
    color: var(--primary-green);
    font-size: 18px;
}

.customer-card {
    display: flex;
    align-items: center;
}

.customer-avatar {
    font-size: 32px;
    margin-right: 12px;
    animation: wiggle 4s infinite;
}

.customer-info {
    flex: 1;
}

.customer-name {
    font-size: 14px;
    font-weight: bold;
    color: var(--text-dark);
    margin-bottom: 4px;
}

.customer-order {
    font-size: 12px;
    color: var(--text-medium);
    margin-bottom: 8px;
}

.patience-bar {
    position: relative;
    background: #E0E0E0;
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
}

.patience-fill {
    background: linear-gradient(90deg, var(--accent-yellow), var(--primary-green));
    height: 100%;
    width: 85%;
    border-radius: 10px;
    animation: pulse 1.5s infinite;
    transition: width 0.3s ease;
}

.patience-text {
    font-size: 10px;
    color: var(--text-medium);
    position: absolute;
    right: 4px;
    top: -16px;
}
