# 新配方实施计划

## 📋 项目概述
添加5个新的高级配方，通过接待顾客数量逐步解锁，丰富游戏内容和玩家体验。

---

## 🍵 新增配方清单

### 1. 桑菊润燥茶 🌿🍇
- **配料**：桑叶 + 杭白菊 + 冰糖
- **功效描述**：疏风清热、缓解秋燥，适合干燥季节饮用
- **解锁条件**：接待30个普通顾客

### 2. 桂花酒酿饮 🌼🍶
- **配料**：酒酿 + 干桂花 + 小圆子
- **功效描述**：清甜暖胃，散发浓郁桂花香，冷热饮皆宜
- **解锁条件**：接待60个普通顾客

### 3. 蜜桃乌龙冷萃 🍑🌱
- **配料**：水蜜桃果肉 + 乌龙茶包 + 蜂蜜
- **功效描述**：果香茶香交融，冰镇后清爽解腻
- **解锁条件**：接待90个普通顾客

### 4. 黄芪枸杞茶 🌰🍯
- **配料**：黄芪片 + 枸杞 + 红枣
- **功效描述**：补气养血，提升免疫力，适合日常养生
- **解锁条件**：接待120个普通顾客

### 5. 竹蔗茅根马蹄水 🍬🪴
- **配料**：竹蔗 + 白茅根 + 马蹄
- **功效描述**：清热润肺、生津止渴，夏日消暑良品
- **解锁条件**：接待150个普通顾客

---

## 🌱 新增种子系统

### 可种植新种子（添加到种子商店）
| 种子名称 | 图标 | 价格 | 生长时间 | 说明 |
|---------|------|------|----------|------|
| 桑叶 | 🌿 | 2铜板 | 45秒 | 用于制作桑菊润燥茶 |
| 杭白菊 | 🌼 | 2铜板 | 50秒 | 优质菊花品种 |
| 水蜜桃 | 🍑 | 3铜板 | 60秒 | 需要加工成果肉 |
| 黄芪 | 🌰 | 3铜板 | 55秒 | 需要加工成黄芪片 |
| 白茅根 | 🪴 | 2铜板 | 40秒 | 清热解毒草药 |
| 马蹄 | ⚪ | 2铜板 | 45秒 | 清甜爽脆 |
| 糯米 | 🌾 | 2铜板 | 50秒 | 需要加工成小圆子 |
| 米 | 🌾 | 1铜板 | 40秒 | 需要加工成米酒 |

### 复用现有种子
- **竹蔗**：复用现有"甘蔗"种子
- **枸杞**：已有
- **红枣**：已有
- **蜂蜜**：已有（商店物品）

---

## 🛒 新增商店物品

### 直接购买物品（添加到商店）
| 物品名称 | 价格 | 说明 |
|---------|------|------|
| 冰糖 | 3铜板 | 清甜调味品 |
| 乌龙茶包 | 4铜板 | 现成茶包 |

---

## ⚙️ 加工系统更新

### 案板新增加工配方（改进版：1原料 → 3成品）
| 原料 | 加工产品 | 产出数量 | 说明 |
|------|----------|----------|------|
| 水蜜桃 | 水蜜桃果肉 | 3份 | 去核切块处理 |
| 黄芪 | 黄芪片 | 3份 | 切片处理 |
| 桂花 | 干桂花 | 3份 | 烘干处理 |
| 糯米 | 小圆子 | 3份 | 搓制成丸 |
| 米 | 米酒 | 3份 | 发酵酿制 |

### 小料加工升级
| 现有小料原料 | 加工产品 | 产出数量 | 改进说明 |
|-------------|----------|----------|----------|
| 甘蔗 | 红糖 | 3份 | 从1份升级为3份 |
| 薄荷 | 薄荷叶 | 3份 | 从1份升级为3份 |
| 生姜 | 姜丝 | 3份 | 从1份升级为3份 |
| 柚子 | 柚子丝 | 3份 | 从1份升级为3份 |
| 银耳 | 银耳丝 | 3份 | 从1份升级为3份 |
| 柠檬 | 柠檬片 | 3份 | 从1份升级为3份 |

### 高级加工链
1. **米 → 米酒 → 酒酿**
   - 米酒可进一步发酵成酒酿（需要研发）
   - 或者直接用米酒+时间制作酒酿

---

## 🎯 解锁机制设计

### 1. 顾客计数器
- 在游戏数据中添加 `servedCustomers` 计数器
- 每次成功服务普通顾客时 +1
- 保存到存档系统中

### 2. 配方解锁检查
- 游戏启动时检查顾客数量
- 达到条件时显示解锁通知
- 新配方自动加入配方面板

### 3. 解锁提示系统
```
🎉 恭喜！你已接待了30位顾客
解锁新配方：桑菊润燥茶
```

---

## 📁 代码实施步骤

### Phase 1: 基础数据更新
1. **更新 HTML 配方面板**
   - 添加5个新配方到 `.recipe-items`
   - 保持现有样式格式

2. **更新种子数据**
   - 在 JavaScript 中添加新种子到 `seedTypes` 数组
   - 更新种子图标映射

3. **更新商店物品**
   - 添加新物品到商店 HTML
   - 移除小圆子和酒酿（改为种植制作）

### Phase 2: 加工系统升级
4. **案板加工配方升级**
   - 修改现有加工逻辑：1原料 → 3成品
   - 添加新的加工按钮和逻辑
   - 实现糯米→小圆子，米→米酒的转换

5. **高级加工链**
   - 研发米酒→酒酿的二次加工
   - 或简化为米直接制作酒酿

### Phase 3: 解锁机制
6. **顾客计数系统**
   - 添加计数器变量
   - 集成到顾客服务逻辑中

7. **配方解锁逻辑**
   - 创建解锁检查函数
   - 实现动态配方显示
   - 添加解锁通知

8. **存档系统更新**
   - 保存顾客计数
   - 保存解锁状态

### Phase 4: 测试与优化
9. **功能测试**
   - 测试种植新种子
   - 测试1→3加工机制
   - 测试配方解锁
   - 测试存档加载

---

## 🎨 UI/UX 改进

### 解锁进度显示
- 在菜单中添加"解锁进度"选项
- 显示当前顾客数量和下一个解锁目标
- 配方面板中灰显未解锁配方

### 加工系统改进
- 加工按钮显示产出数量 "糯米 → 小圆子x3"
- 加工成功时显示获得数量提示
- 更清晰的加工反馈

### 通知系统
- 解锁新配方时的动画效果
- 加工成功的数量提示
- 成就感提升

---

## 🔧 技术考虑

### 兼容性
- 确保新功能与现有存档兼容
- 老存档加载时初始化新数据
- 平滑升级加工系统（1→3）

### 性能
- 优化配方检查逻辑
- 避免频繁的DOM操作
- 合理的数据结构设计

### 可扩展性
- 为未来更多配方预留接口
- 模块化的解锁系统设计
- 灵活的加工链系统

---

## ✅ 验收标准

### 功能完整性
- [ ] 5个新配方正确显示
- [ ] 所有新种子可正常种植
- [ ] 加工系统1→3正常工作
- [ ] 糯米→小圆子，米→米酒正常
- [ ] 解锁机制准确触发
- [ ] 存档系统包含新数据

### 用户体验
- [ ] 解锁通知清晰明确
- [ ] 进度显示直观
- [ ] 加工反馈清晰
- [ ] 操作流程顺畅
- [ ] 视觉效果统一

### 经济平衡
- [ ] 种植成本合理
- [ ] 加工产出平衡（1→3）
- [ ] 配方制作成本适中
- [ ] 游戏进度平稳

### 稳定性
- [ ] 无错误和崩溃
- [ ] 存档加载正常
- [ ] 性能表现良好

---

**预估开发时间**：2-3小时  
**优先级**：高  
**风险评估**：低（基于现有系统扩展）  
**经济影响**：提升游戏深度，增加种植价值 