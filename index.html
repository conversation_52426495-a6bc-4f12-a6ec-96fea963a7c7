<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Tea Shop - Mobile</title>
    <link rel="stylesheet" href="tea_shop_mobile.css">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#4CAF50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Tea Shop">
    <link rel="icon" href="icon.svg" type="image/svg+xml">
    <script>
        // 注册Service Worker以支持离线使用
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/service-worker.js')
                    .then(registration => {
                        console.log('ServiceWorker注册成功:', registration.scope);
                    })
                    .catch(error => {
                        console.log('ServiceWorker注册失败:', error);
                    });
            });
        }
    </script>
</head>
<body>
    <div class="container">
        <!-- 顶部控制区 -->
        <header>
            <div class="top-bar">
                <div class="coins-display">
                    <span>铜板: </span>
                    <span id="coins-count">100</span>
                </div>
                <div class="title">Tea Shop</div>
                <div class="menu-btn" id="menu-btn">≡</div>
            </div>
            
            <!-- 菜单面板 -->
            <div class="menu-panel" id="menu-panel">
                <button id="save-manager">存档管理</button>
                <button id="recipe-button">查看配方</button>
                <button id="collection-button">集卡册</button>
                <button id="unlock-progress-button">解锁进度</button>
                <button id="test-mode-button">测试模式</button>
                <button id="test-page-button">完整测试页</button>
                <button id="pause-btn" class="menu-pause-btn">暂停游戏</button>
                <button id="close-menu">关闭</button>
            </div>
        </header>

        <!-- 添加测试模式面板 -->
        <div class="test-mode-panel" id="test-mode-panel" style="display: none;">
            <div class="test-header">
                <div class="test-title">测试模式</div>
                <button id="close-test-mode" class="close-btn">×</button>
            </div>
            <div class="test-content">
                <div class="test-instruction">这里是测试模式，您可以测试顾客提取茶饮的功能，不会影响原有游戏。</div>
                <div class="test-controls">
                    <button id="spawn-customer-test">生成顾客</button>
                    <button id="add-tea-test">快速制作茶饮</button>
                    <button id="exit-test-mode">退出测试</button>
                </div>
                <div class="test-status">
                    <div>测试状态：<span id="test-status-text">准备就绪</span></div>
                </div>
            </div>
        </div>

        <!-- 信息面板 (可滑动查看) -->
        <div class="info-swiper">
            
                <div class="swiper-wrapper">
                    <!-- 天气与季节 -->
                    <div class="swiper-slide weather-season">
                                                    <div class="info-block">
                                <div class="game-status">
                                    <div class="status-info">
                                        <span class="season-badge" id="season">春天</span>
                                        <span class="weather-badge" id="weather">晴天</span>
                                        <span class="day-badge">第<span id="day">1</span>天</span>
                                    </div>
                                    <div class="status-buttons">
                                        <button id="basket-btn" class="status-btn basket-btn" title="查看篮子">🧺</button>
                                        <button id="buy-seed-status" class="status-btn shop-btn" title="打开商店">🛒</button>
                                    </div>
                                </div>
                            <div class="customer-status">
                                <div class="customer-info-row">
                                    <span class="info-label">顾客:</span>
                                    <span id="customer-name">暂无顾客</span>
                                    <span class="info-label">耐心:</span>
                                    <span id="patience-timer">-</span>秒
                                </div>
                                <div class="customer-info-row">
                                    <span class="info-label">想要:</span>
                                    <span id="customer-tea">-</span>
                                    <span class="info-label">加料:</span>
                                    <span id="customer-toppings">-</span>
                                </div>
                            </div>
                       
                    </div>
                    
                    <!-- 消息记录 -->
                    <div class="swiper-slide message-log">

                        <div class="message-content">
                            <div class="message">欢迎来到古风茶铺!</div>
                        </div>
                    </div>
                </div>
                
                <!-- 指示器 -->
                <div class="swiper-pagination"></div>
            </div>
        </div>

        <!-- 种子信息 -->
        <!-- <div class="seed-info">
            <span id="selected-seed">请选择种子类型</span>
        </div> -->

        <!-- 种子/商店面板 -->
        <div class="shop-panel" id="seed-panel">
            <div class="shop-header">
                <div id="selected-shop-item">请选择商品</div>
                <div class="coins-display shop-coins">
                    <span>铜板: </span>
                    <span id="shop-coins-count">100</span>
                </div>
                <button id="close-shop" class="close-btn">×</button>
            </div>
            
            <div class="shop-content">
                <div class="shop-items-container">
                    <!-- 种子区域 -->
                    <div class="shop-section">
                        <div class="shop-section-title">种子</div>
                        <div class="seed-grid">
                            <button class="seed-btn" data-price="1">五味子</button>
                            <button class="seed-btn" data-price="1">乌梅</button>
                            <button class="seed-btn" data-price="1">山楂</button>
                            <button class="seed-btn" data-price="1">陈皮</button>
                            <button class="seed-btn" data-price="1">甘草</button>
                            <button class="seed-btn" data-price="1">桂花</button>
                            <button class="seed-btn" data-price="1">大麦</button>
                            <button class="seed-btn" data-price="1">菊花</button>
                            <button class="seed-btn" data-price="1">金银花</button>
                            <button class="seed-btn" data-price="1">决明子</button>
                            <button class="seed-btn" data-price="1">枸杞</button>
                            <button class="seed-btn" data-price="1">生姜</button>
                            <button class="seed-btn" data-price="1">桂圆</button>
                            <button class="seed-btn" data-price="1">红枣</button>
                            <button class="seed-btn" data-price="1">薄荷</button>
                            <button class="seed-btn" data-price="1">玫瑰花</button>
                            <button class="seed-btn" data-price="1">洛神花</button>
                            <button class="seed-btn" data-price="1">冬瓜</button>
                            <button class="seed-btn" data-price="1">荷叶</button>
                            <button class="seed-btn" data-price="1">薏米</button>
                            <button class="seed-btn" data-price="1">雪花梨</button>
                            <button class="seed-btn" data-price="1">甘蔗</button>
                            <button class="seed-btn" data-price="1">柚子</button>
                            <button class="seed-btn" data-price="1">话梅</button>
                            <button class="seed-btn" data-price="1">柠檬</button>
                            <!-- 新增种子 -->
                            <button class="seed-btn" data-price="2">桑叶</button>
                            <button class="seed-btn" data-price="2">杭白菊</button>
                            <button class="seed-btn" data-price="3">水蜜桃</button>
                            <button class="seed-btn" data-price="3">黄芪</button>
                            <button class="seed-btn" data-price="2">白茅根</button>
                            <button class="seed-btn" data-price="2">马蹄</button>
                            <button class="seed-btn" data-price="2">糯米</button>
                            <button class="seed-btn" data-price="1">米</button>
                        </div>
                    </div>

                    <!-- 物品区域 -->
                    <div class="shop-section">
                        <div class="shop-section-title">物品</div>
                        <div class="item-grid">
                            <button class="shop-item-btn" data-item="蜂蜜" data-price="3">蜂蜜 (3铜板)</button>
                            <button class="shop-item-btn" data-item="银耳" data-price="3">银耳 (3铜板)</button>
                            <button class="shop-item-btn" data-item="红糖" data-price="2">红糖 (2铜板)</button>
                            <button class="shop-item-btn" data-item="薄荷叶" data-price="2">薄荷叶 (2铜板)</button>
                            <!-- 新增物品 -->
                            <button class="shop-item-btn" data-item="冰糖" data-price="3">冰糖 (3铜板)</button>
                            <button class="shop-item-btn" data-item="乌龙茶包" data-price="4">乌龙茶包 (4铜板)</button>
                        </div>
                    </div>

                    <!-- 购物车预览 -->
                    <div class="cart-preview">
                        <div class="cart-preview-total">
                            购物车: <span id="cart-items-count">0</span> 个物品
                            总计: <span id="cart-total">0</span> 铜板
                        </div>
                        <button class="cart-preview-button" id="checkout-btn">结账</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加到购物车提示 -->
        <div class="add-to-cart-hint" id="add-to-cart-hint" style="display: none;">
            已添加到购物车
        </div>

        <!-- 小篮子选择窗口 -->
        <div class="basket-select-panel" id="basket-select-panel">
            <div class="basket-select-header">
                <div class="basket-select-title">选择种植物品</div>
                <button id="confirm-plant-btn" class="confirm-plant-top-btn" disabled>确认种植</button>
                <button id="close-basket-select" class="close-btn">×</button>
            </div>
            <div class="basket-select-content">
                <div class="basket-select-info">
                    <div class="selected-plot-info">
                        <span>田地: </span><span id="selected-plot-name">1号田地</span>
                    </div>
                </div>
                
                <div class="seed-selection-section">
                    <div class="seed-section-title">库存种子：</div>
                    <div class="seed-grid available-seeds" id="available-seeds-grid">
                        <!-- 有库存的种子将在这里动态生成 -->
                    </div>
                </div>
                
                <div class="seed-selection-section">
                    <div class="seed-section-title">缺货种子（点击购买）：</div>
                    <div class="seed-grid unavailable-seeds" id="unavailable-seeds-grid">
                        <!-- 没有库存的种子将在这里动态生成 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 配方面板 -->
        <div class="recipe-panel" id="recipe-panel">
            <div class="recipe-select-header">
                <div class="recipe-select-title">茶饮配方</div>
                <button id="close-recipe" class="close-btn">×</button>
            </div>
            <div class="recipe-items">
                <div class="recipe-item">
                    <div class="recipe-name">五味子饮</div>
                    <div class="recipe-ingredients">五味子</div>
                </div>
                <div class="recipe-item">
                    <div class="recipe-name">古法酸梅汤</div>
                    <div class="recipe-ingredients">乌梅, 山楂, 陈皮, 甘草, 桂花</div>
                </div>
                <div class="recipe-item">
                    <div class="recipe-name">焦香大麦茶</div>
                    <div class="recipe-ingredients">大麦</div>
                </div>
                <div class="recipe-item">
                    <div class="recipe-name">三花决明茶</div>
                    <div class="recipe-ingredients">菊花, 金银花, 决明子, 枸杞</div>
                </div>
                <div class="recipe-item">
                    <div class="recipe-name">陈皮姜米茶</div>
                    <div class="recipe-ingredients">陈皮, 生姜</div>
                </div>
                <div class="recipe-item">
                    <div class="recipe-name">桂圆红枣茶</div>
                    <div class="recipe-ingredients">桂圆, 红枣, 枸杞</div>
                </div>
                <div class="recipe-item">
                    <div class="recipe-name">薄荷甘草凉茶</div>
                    <div class="recipe-ingredients">薄荷, 甘草</div>
                </div>
                <div class="recipe-item">
                    <div class="recipe-name">洛神玫瑰饮</div>
                    <div class="recipe-ingredients">洛神花, 玫瑰花, 山楂</div>
                </div>
                <div class="recipe-item">
                    <div class="recipe-name">冬瓜荷叶饮</div>
                    <div class="recipe-ingredients">冬瓜, 荷叶, 薏米</div>
                </div>
                <div class="recipe-item">
                    <div class="recipe-name">小吊梨汤</div>
                    <div class="recipe-ingredients">雪花梨, 银耳, 话梅, 枸杞</div>
                </div>
                <div class="recipe-item">
                    <div class="recipe-name">柠檬茶</div>
                    <div class="recipe-ingredients">柠檬</div>
                </div>
                <!-- 新增解锁配方 -->
                <div class="recipe-item unlockable-recipe" data-unlock="30" style="display: none;">
                    <div class="recipe-name">桑菊润燥茶</div>
                    <div class="recipe-ingredients">桑叶, 杭白菊, 冰糖</div>
                </div>
                <div class="recipe-item unlockable-recipe" data-unlock="60" style="display: none;">
                    <div class="recipe-name">桂花酒酿饮</div>
                    <div class="recipe-ingredients">酒酿, 干桂花, 小圆子</div>
                </div>
                <div class="recipe-item unlockable-recipe" data-unlock="90" style="display: none;">
                    <div class="recipe-name">蜜桃乌龙冷萃</div>
                    <div class="recipe-ingredients">水蜜桃果肉, 乌龙茶包, 蜂蜜</div>
                </div>
                <div class="recipe-item unlockable-recipe" data-unlock="120" style="display: none;">
                    <div class="recipe-name">黄芪枸杞茶</div>
                    <div class="recipe-ingredients">黄芪片, 枸杞, 红枣</div>
                </div>
                <div class="recipe-item unlockable-recipe" data-unlock="150" style="display: none;">
                    <div class="recipe-name">竹蔗茅根马蹄水</div>
                    <div class="recipe-ingredients">甘蔗, 白茅根, 马蹄</div>
                </div>
            </div>
        </div>

        <!-- 主游戏区域 - 分成选项卡 -->
        <div class="game-tabs">
            <button class="game-tab active" data-tab="farm-tab">种植</button>
            <button class="game-tab" data-tab="kitchen-tab">厨房</button>
            <button class="game-tab" data-tab="tea-tab">茶摊</button>
        </div>

        <!-- 种植选项卡 -->
        <div class="game-content active" id="farm-tab">
                <div class="farm-section">

                    <div class="plots-container">
                        <div class="plot" id="plot-0">
                            <div class="plot-checkbox-container">
                                <input type="checkbox" class="plot-checkbox" data-plot-id="0">
                                <label>1号田地</label>
                            </div>
                            <div class="plot-info">
                                <div class="plot-row">
                                    <div class="plot-label">物品:</div>
                                <div class="plot-value plot-name clickable-item" data-plot-id="0">空地</div>
                                </div>
                                <div class="plot-row">
                                    <div class="plot-label">湿度:</div>
                                    <div class="plot-value plot-moisture">50%</div>
                                </div>
                                <div class="plot-row">
                                    <div class="plot-label">肥沃:</div>
                                    <div class="plot-value plot-fertility">50%</div>
                                </div>
                                <div class="plot-row">
                                    <div class="plot-label">生长:</div>
                                    <div class="plot-value plot-stage">-</div>
                                    <div class="plot-icon"></div>
                                </div>
                                <div class="plot-row">
                                    <div class="plot-label">计时:</div>
                                    <div class="plot-value plot-timer">-</div>
                                </div>
                            </div>
                        </div>
                        <div class="plot" id="plot-1">
                            <div class="plot-checkbox-container">
                                <input type="checkbox" class="plot-checkbox" data-plot-id="1">
                                <label>2号田地</label>
                            </div>
                            <div class="plot-info">
                                <div class="plot-row">
                                    <div class="plot-label">物品:</div>
                                <div class="plot-value plot-name clickable-item" data-plot-id="1">空地</div>
                            </div>
                            <div class="plot-row">
                                <div class="plot-label">湿度:</div>
                                <div class="plot-value plot-moisture">50%</div>
                            </div>
                            <div class="plot-row">
                                <div class="plot-label">肥沃:</div>
                                <div class="plot-value plot-fertility">50%</div>
                            </div>
                            <div class="plot-row">
                                <div class="plot-label">生长:</div>
                                <div class="plot-value plot-stage">-</div>
                                <div class="plot-icon"></div>
                            </div>
                            <div class="plot-row">
                                <div class="plot-label">计时:</div>
                                <div class="plot-value plot-timer">-</div>
                            </div>
                        </div>
                    </div>
                    <div class="plot" id="plot-2">
                        <div class="plot-checkbox-container">
                            <input type="checkbox" class="plot-checkbox" data-plot-id="2">
                            <label>3号田地</label>
                        </div>
                        <div class="plot-info">
                            <div class="plot-row">
                                <div class="plot-label">物品:</div>
                                <div class="plot-value plot-name clickable-item" data-plot-id="2">空地</div>
                            </div>
                            <div class="plot-row">
                                <div class="plot-label">湿度:</div>
                                <div class="plot-value plot-moisture">50%</div>
                            </div>
                            <div class="plot-row">
                                <div class="plot-label">肥沃:</div>
                                <div class="plot-value plot-fertility">50%</div>
                            </div>
                            <div class="plot-row">
                                <div class="plot-label">生长:</div>
                                <div class="plot-value plot-stage">-</div>
                                <div class="plot-icon"></div>
                            </div>
                            <div class="plot-row">
                                <div class="plot-label">计时:</div>
                                <div class="plot-value plot-timer">-</div>
                            </div>
                        </div>
                    </div>
                    <div class="plot" id="plot-3">
                        <div class="plot-checkbox-container">
                            <input type="checkbox" class="plot-checkbox" data-plot-id="3">
                            <label>4号田地</label>
                        </div>
                        <div class="plot-info">
                            <div class="plot-row">
                                <div class="plot-label">物品:</div>
                                <div class="plot-value plot-name clickable-item" data-plot-id="3">空地</div>
                                </div>
                                <div class="plot-row">
                                    <div class="plot-label">湿度:</div>
                                    <div class="plot-value plot-moisture">50%</div>
                                </div>
                                <div class="plot-row">
                                    <div class="plot-label">肥沃:</div>
                                    <div class="plot-value plot-fertility">50%</div>
                                </div>
                                <div class="plot-row">
                                    <div class="plot-label">生长:</div>
                                    <div class="plot-value plot-stage">-</div>
                                    <div class="plot-icon"></div>
                                </div>
                                <div class="plot-row">
                                    <div class="plot-label">计时:</div>
                                    <div class="plot-value plot-timer">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                    </div>
                    
        <!-- 厨房选项卡 -->
        <div class="game-content" id="kitchen-tab">
            <!-- 炉灶 -->
            <div class="stoves-container">
                <div class="stoves">
                    <div class="stove" id="stove-1">
                        <div class="stove-label">炉灶1</div>
                        <div class="stove-state">点击放水</div>
                    </div>
                    <div class="stove" id="stove-2">
                        <div class="stove-label">炉灶2</div>
                        <div class="stove-state">点击放水</div>
                    </div>
                </div>
            </div>
            
            <!-- 案板 -->
            <div class="processing-container">
                <div class="processing-board" id="processing-board">
                    <div class="processing-label">案板</div>
                    <div class="processing-state">点击加工材料</div>
                </div>
                <div class="processing-recipes">
                    <button class="recipe-btn" data-recipe="红糖" data-output="3">甘蔗→红糖x3</button>
                    <button class="recipe-btn" data-recipe="薄荷叶" data-output="3">薄荷→薄荷叶x3</button>
                    <button class="recipe-btn" data-recipe="姜丝" data-output="3">生姜→姜丝x3</button>
                    <button class="recipe-btn" data-recipe="柚子丝" data-output="3">柚子→柚子丝x3</button>
                    <button class="recipe-btn" data-recipe="银耳丝" data-output="3">银耳→银耳丝x3</button>
                    <button class="recipe-btn" data-recipe="柠檬片" data-output="3">柠檬→柠檬片x3</button>
                    <!-- 新增加工配方 -->
                    <button class="recipe-btn" data-recipe="水蜜桃果肉" data-output="3">水蜜桃→果肉x3</button>
                    <button class="recipe-btn" data-recipe="黄芪片" data-output="3">黄芪→黄芪片x3</button>
                    <button class="recipe-btn" data-recipe="干桂花" data-output="3">桂花→干桂花x3</button>
                    <button class="recipe-btn" data-recipe="小圆子" data-output="3">糯米→小圆子x3</button>
                    <button class="recipe-btn" data-recipe="米酒" data-output="3">米→米酒x3</button>
                </div>
            </div>
        </div>

        <!-- 茶摊选项卡 -->
        <div class="game-content" id="tea-tab">
            <!-- 信息窗口占位，后续插入 -->
            <div id="tea-info-panel"></div>
            <!-- 茶饮展示 -->
            <div class="tea-container">
                <div class="tea-display">
                    <div class="no-tea">还没有制作好的茶饮</div>
                </div>
                <!-- 小料按钮放在茶饮下面 -->
                <div class="section-title toppings-header">
                    <div class="toppings-display" id="toppings-display">
                        <!-- 小料将在这里动态生成 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 炉灶配方选择面板 -->
        <div class="recipe-select-panel" id="recipe-select-panel">
            <div class="recipe-select-header">
                <div class="recipe-select-title">选择茶饮配方</div>
                <button id="close-recipe-select" class="close-btn">×</button>
            </div>
            <div class="recipe-select-content">
                <div class="recipe-select-list">
                    <!-- 配方列表将在JS中动态生成 -->
                </div>
                <div class="recipe-select-info">
                    <div class="recipe-select-description">
                        <div id="selected-recipe-name">请选择茶饮</div>
                        <div id="selected-recipe-ingredients">需要的材料将显示在这里</div>
                    </div>
                    <div class="recipe-select-buttons">
                        <button id="make-recipe-btn" disabled>制作茶饮</button>
                        <button id="cancel-recipe-btn">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 解锁进度面板 -->
    <div class="unlock-progress-panel" id="unlock-progress-panel" style="display: none;">
        <div class="unlock-header">
            <div class="unlock-title">配方解锁进度</div>
            <button id="close-unlock-progress" class="close-btn">×</button>
        </div>
        <div class="unlock-content">
            <div class="progress-info">
                <div class="current-progress">
                    已服务顾客: <span id="served-customers-count">0</span>位
                </div>
                <div class="next-unlock">
                    距离下一个解锁: <span id="next-unlock-info">查看下方列表</span>
                </div>
            </div>
            
            <div class="unlock-recipe-list">
                <div class="unlock-section-title">配方解锁条件</div>
                <div class="unlock-recipes" id="unlock-recipes-list">
                    <!-- 解锁配方列表将在JavaScript中动态生成 -->
                </div>
            </div>
        </div>
            </div>

        <!-- 篮子查看面板 -->
        <div class="basket-view-panel" id="basket-view-panel" style="display: none;">
            <div class="basket-view-header">
                <div class="basket-view-title">🧺 我的篮子</div>
                <button id="close-basket-view" class="close-btn">×</button>
            </div>
            <div class="basket-view-content">
                <div class="basket-view-info">
                    <div class="basket-section">
                        <div class="basket-section-title">🌱 种子库存</div>
                        <div class="basket-items" id="basket-seeds-display">
                            <!-- 种子将在这里动态生成 -->
                        </div>
                    </div>
                    
                    <div class="basket-section">
                        <div class="basket-section-title">🌿 收获物品</div>
                        <div class="basket-items" id="basket-materials-display">
                            <!-- 收获物品将在这里动态生成 -->
                        </div>
                    </div>
                    
                    <div class="basket-section">
                        <div class="basket-section-title">🧂 小料库存</div>
                        <div class="basket-items" id="basket-toppings-display">
                            <!-- 小料将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 暂停遮罩 -->
        <div class="pause-overlay" id="pause-overlay">
            <div>游戏已暂停</div>
            <button class="resume-btn" id="resume-btn">继续游戏</button>
        </div>

    <script src="tea_shop_mobile.js"></script>
</body>
</html> 