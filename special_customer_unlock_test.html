<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>特殊顾客解锁流程测试</title>
    <style>
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #7c4f3f 0%, #a0756b 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .header p {
            margin: 10px 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }
        
        .customer-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .customer-section:hover {
            border-color: #7c4f3f;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(124, 79, 63, 0.15);
        }
        
        .customer-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #7c4f3f;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .customer-name {
            font-size: 18px;
            font-weight: 600;
            color: #7c4f3f;
            margin-bottom: 8px;
        }
        
        .recipe-name {
            font-size: 16px;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .visit-info {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
        }
        
        .visit-count {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 20px;
            font-weight: 500;
            display: inline-block;
            margin-bottom: 10px;
        }
        
        .unlock-status {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 10px;
            display: inline-block;
        }
        
        .status-locked {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .status-unlocked {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .test-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .test-btn {
            background: #7c4f3f;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            flex: 1;
            min-width: 100px;
        }
        
        .test-btn:hover {
            background: #6a4334;
            transform: translateY(-1px);
        }
        
        .test-btn:active {
            transform: translateY(0);
        }
        
        .test-btn.disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .control-section {
            border-top: 1px solid #eee;
            padding: 30px;
            background: #fafbfc;
        }
        
        .control-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .control-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 15px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .control-btn.danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }
        
        .control-btn.danger:hover {
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }
        
        .log-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .log-header {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .log-content {
            height: 250px;
            overflow-y: auto;
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #dee2e6;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .log-entry {
            padding: 4px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .log-time {
            color: #666;
            margin-right: 10px;
        }
        
        .log-type-info {
            color: #0066cc;
        }
        
        .log-type-success {
            color: #00cc66;
            font-weight: 600;
        }
        
        .log-type-warning {
            color: #ff8800;
        }
        
        .log-type-error {
            color: #cc0000;
        }
        
        /* 故事弹窗样式 */
        .recipe-unlock-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 90%;
            max-width: 450px;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            z-index: 2000;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            animation: unlock-panel-appear 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
            backdrop-filter: blur(10px);
        }
        
        @keyframes unlock-panel-appear {
            from { 
                opacity: 0; 
                transform: translate(-50%, -60%) scale(0.8); 
            }
            to { 
                opacity: 1; 
                transform: translate(-50%, -50%) scale(1); 
            }
        }
        
        .unlock-header {
            padding: 20px 25px 15px;
            background: linear-gradient(135deg, #7c4f3f 0%, #a0756b 100%);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .unlock-header h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 28px;
            color: white;
            cursor: pointer;
            padding: 0;
            line-height: 1;
            opacity: 0.8;
            transition: opacity 0.2s;
        }
        
        .close-btn:hover {
            opacity: 1;
        }
        
        .unlock-content {
            padding: 25px;
            overflow-y: auto;
            max-height: 70vh;
        }
        
        .unlock-image {
            font-size: 60px;
            text-align: center;
            margin-bottom: 20px;
            animation: tea-bounce 2s ease-in-out infinite;
        }
        
        @keyframes tea-bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }
        
        .unlock-title {
            font-size: 26px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 25px;
            color: #7c4f3f;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }
        
        .unlock-story {
            font-size: 16px;
            line-height: 1.7;
            margin-bottom: 25px;
            color: #333;
            text-align: justify;
            padding: 0 5px;
            text-indent: 2em;
        }
        
        .unlock-divider {
            height: 2px;
            background: linear-gradient(90deg, transparent, #7c4f3f, transparent);
            margin: 20px 0;
        }
        
        .unlock-info {
            font-size: 15px;
            color: #555;
            line-height: 1.6;
        }
        
        .unlock-effect, .unlock-ingredients {
            margin-bottom: 12px;
            padding: 10px 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #7c4f3f;
        }
        
        .unlock-footer {
            padding: 20px 25px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
            text-align: center;
        }
        
        .confirm-btn {
            padding: 12px 30px;
            background: linear-gradient(135deg, #7c4f3f 0%, #a0756b 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(124, 79, 63, 0.3);
        }
        
        .confirm-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(124, 79, 63, 0.4);
        }
        
        .panel-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1999;
            animation: overlay-appear 0.3s ease-out;
        }
        
        @keyframes overlay-appear {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
                padding: 20px;
                gap: 20px;
            }
            
            .control-grid {
                grid-template-columns: 1fr;
            }
            
            .recipe-unlock-panel {
                width: 95%;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>🍵 特殊顾客解锁流程测试</h1>
            <p>测试特殊顾客解锁配方的完整流程，包括概率机制和故事弹窗</p>
        </div>
        
        <div class="test-grid">
            <div class="customer-section">
                <h3 style="margin-top: 0; color: #7c4f3f; font-size: 20px;">⭐ 必定解锁 (1-2次)</h3>
                <div id="guaranteed-customers"></div>
            </div>
            
            <div class="customer-section">
                <h3 style="margin-top: 0; color: #7c4f3f; font-size: 20px;">🎲 概率解锁 (2-5次)</h3>
                <div id="probability-customers"></div>
            </div>
        </div>
        
        <div class="control-section">
            <div class="control-grid">
                <button class="control-btn" onclick="testAllCustomersOnce()">
                    🧪 批量测试 - 所有顾客来访1次
                </button>
                <button class="control-btn" onclick="showRandomStory()">
                    📖 随机显示故事
                </button>
                <button class="control-btn danger" onclick="resetAllProgress()">
                    🔄 重置所有进度
                </button>
            </div>
            
            <div class="log-section">
                <div class="log-header">📋 测试日志</div>
                <div class="log-content" id="test-log"></div>
            </div>
        </div>
    </div>

    <script>
        // 测试用的游戏数据
        const testGameData = {
            unlockedRecipes: ["五味子饮", "柠檬茶"],
            customerVisits: {},
            shownRecipeStories: [],
            
            // 解锁规则
            recipeUnlockRules: {
                "洛神玫瑰饮": { customer: "凌小路", visitsRequired: 1, chance: 1.0, guaranteedOnVisit: 1 },
                "桂圆红枣茶": { customer: "花花", visitsRequired: 1, chance: 1.0, guaranteedOnVisit: 1 },
                "焦香大麦茶": { customer: "江飞飞", visitsRequired: 2, chance: 1.0, guaranteedOnVisit: 2 },
                "三花决明茶": { customer: "江三", visitsRequired: 2, chance: 1.0, guaranteedOnVisit: 2 },
                "薄荷甘草凉茶": { customer: "江四", visitsRequired: 2, chance: 1.0, guaranteedOnVisit: 2 },
                "陈皮姜米茶": { customer: "池云旗", visitsRequired: 2, chance: 0.5, guaranteedOnVisit: 3 },
                "冬瓜荷叶饮": { customer: "江潮", visitsRequired: 3, chance: 0.6, guaranteedOnVisit: 4 },
                "古法酸梅汤": { customer: "池惊暮", visitsRequired: 2, chance: 0.3, guaranteedOnVisit: 3 },
                "小吊梨汤": { customer: "江敕封", visitsRequired: 3, chance: 0.4, guaranteedOnVisit: 5 }
            },
            
            // 故事数据
            recipeStories: {
                "洛神玫瑰饮": {
                    customer: "凌小路",
                    title: "朱砂",
                    story: "凌小路袖中藏着一盏温热的洛神玫瑰饮。'疏肝解郁的，好好学学，飞飞来了就做给他。跟他说就说养颜的茶方子'挑眉笑时，眼底却映着刀光，袍角还沾着血。",
                    effect: "疏肝解郁，美白养颜，活血调经，适合女子日常饮用"
                },
                "桂圆红枣茶": {
                    customer: "花花",
                    title: "无归",
                    story: "花花去凌雪坟前扫墓，手里拿着他最喜欢她给他做的茶。只是这一次只能自己做了。'自己给自己作茶怎么行，这方子给你们，以后我就来这里喝吧'",
                    effect: "补血益气，安神养心，滋阴润燥，适合体弱或熬夜者饮用"
                },
                "焦香大麦茶": {
                    customer: "江飞飞",
                    title: "雪夜",
                    story: "长安冬夜，江飞飞蜷在凌雪阁的屋檐上，指尖冻得发僵。江三翻上屋顶，扔来一壶滚烫的大麦茶：'怂样，喝两口。'茶雾氤氲里，他忽然想起幼时第一次握刀，也是这焦苦的甜香压住了颤抖。",
                    effect: "暖胃消食，缓解焦虑，安定心神，适合秋冬饮用"
                },
                "三花决明茶": {
                    customer: "江三",
                    title: "夜狩",
                    story: "江四执刀归来，见江三伏案瞌睡，手边一盏凉透的三花决明茶。他轻叹，将外袍披上兄长肩头——却不知昨夜自己任务单上那三个名字，早已被江三的血刃划去。茶渣沉底，如未愈的旧伤。",
                    effect: "清肝明目，清热解毒，缓解眼疲劳，适合长期伏案或夜视者饮用"
                },
                "薄荷甘草凉茶": {
                    customer: "江四",
                    title: "三哥",
                    story: "江四给江三泡的茶，清清凉凉的，他那么爱出汗，肯定喜欢。茶叶刚放下，就听到三哥在院子里训练的刀声，他悄悄探头看了一眼，决定加多一片薄荷叶。",
                    effect: "清热解暑，润喉止咳，提神醒脑，适合夏季饮用"
                },
                "陈皮姜米茶": {
                    customer: "池云旗",
                    title: "师徒",
                    story: "池云旗心疼那小家伙，以前也不懂自己照顾自己，这茶是她专门给他找医师抄的方子。'别总吃那些乱七八糟的东西，胃疼了可别来找师父'虽然嘴上这么说，她还是悄悄在茶里多加了一片陈皮。",
                    effect: "健脾和胃，理气化痰，温中散寒，适合消化不良或胃寒者饮用"
                },
                "冬瓜荷叶饮": {
                    customer: "江潮",
                    title: "师徒2",
                    story: "江潮给师父弄的消暑茶，荷叶是自己趴在池塘边采的，冬瓜也是自己种的。'师父，您尝尝，我按照您说的方法做的'他小心翼翼地端着茶，生怕师父不喜欢，却不知道池云旗早已欣慰地笑了。",
                    effect: "清热利湿，消肿减脂，美容养颜，适合夏季消暑或减肥者饮用"
                },
                "古法酸梅汤": {
                    customer: "池惊暮",
                    title: "梅香",
                    story: "长安暑夜，池惊暮执剑伏于屋脊。目标出现时，她正饮尽最后一滴酸梅汤。瓷碗坠地碎响混着喉骨断裂声，梅妃教的小方子——杀人时唇齿间该留着甜味，才不苦。",
                    effect: "生津止渴，消暑解腻，健脾开胃，缓解燥热，唐代已是宫廷消暑佳饮"
                },
                "小吊梨汤": {
                    customer: "江敕封",
                    title: "琴心",
                    story: "江敕封抚琴时总爱在身边放一盏小吊梨汤，琴声悠扬，茶香袅袅。他说琴如人生，需要慢慢调教；茶如心境，需要细细品味。一曲终了，一盏茶尽，都是这世间最温柔的时光。",
                    effect: "润肺止咳，清热降火，滋阴美容，宫廷传统滋补佳品"
                }
            },
            
            // 配方材料（用于显示）
            recipeIngredients: {
                "洛神玫瑰饮": ["洛神花", "玫瑰花", "山楂"],
                "桂圆红枣茶": ["桂圆", "红枣", "枸杞"],
                "焦香大麦茶": ["大麦"],
                "三花决明茶": ["菊花", "金银花", "决明子", "枸杞"],
                "薄荷甘草凉茶": ["薄荷", "甘草"],
                "陈皮姜米茶": ["陈皮", "生姜"],
                "冬瓜荷叶饮": ["冬瓜", "荷叶", "薏米"],
                "古法酸梅汤": ["乌梅", "山楂", "陈皮", "甘草", "桂花"],
                "小吊梨汤": ["雪花梨", "银耳", "话梅", "枸杞"]
            }
        };
        
        // 日志功能
        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const time = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `
                <span class="log-time">[${time}]</span>
                <span class="log-type-${type}">${message}</span>
            `;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        // 顾客来访测试
        function testCustomerVisit(customerName) {
            log(`🏃 ${customerName} 来到茶铺`, 'info');
            
            // 增加来访次数
            if (!testGameData.customerVisits[customerName]) {
                testGameData.customerVisits[customerName] = 1;
            } else {
                testGameData.customerVisits[customerName]++;
            }
            
            const visitCount = testGameData.customerVisits[customerName];
            log(`${customerName} 已来访 ${visitCount} 次`, 'info');
            
            // 检查解锁
            const unlocked = checkRecipeUnlock(customerName);
            if (!unlocked) {
                log(`❌ 暂未解锁新配方`, 'warning');
            }
            
            updateCustomerDisplay();
        }
        
        // 检查配方解锁
        function checkRecipeUnlock(customerName) {
            const visitCount = testGameData.customerVisits[customerName] || 0;
            let unlockedRecipe = null;
            
            Object.entries(testGameData.recipeUnlockRules).forEach(([recipe, rule]) => {
                if (rule.customer === customerName) {
                    if (testGameData.unlockedRecipes.includes(recipe)) {
                        return;
                    }
                    
                    if (visitCount >= rule.visitsRequired) {
                        let shouldUnlock = false;
                        
                        if (visitCount >= rule.guaranteedOnVisit) {
                            shouldUnlock = true;
                            log(`🎯 达到保底次数 ${rule.guaranteedOnVisit}，必定解锁`, 'info');
                        } else {
                            const random = Math.random();
                            if (random < rule.chance) {
                                shouldUnlock = true;
                                log(`🎲 概率解锁成功 (${(rule.chance * 100).toFixed(0)}%，随机值: ${random.toFixed(3)})`, 'info');
                            } else {
                                log(`🎲 概率解锁失败 (${(rule.chance * 100).toFixed(0)}%，随机值: ${random.toFixed(3)})`, 'warning');
                            }
                        }
                        
                        if (shouldUnlock) {
                            unlockedRecipe = recipe;
                        }
                    } else {
                        log(`❌ 访问次数不足 (需要${rule.visitsRequired}次，当前${visitCount}次)`, 'warning');
                    }
                }
            });
            
            if (unlockedRecipe) {
                log(`🎉 解锁配方: ${unlockedRecipe}`, 'success');
                testGameData.unlockedRecipes.push(unlockedRecipe);
                
                // 延迟显示故事，模拟真实游戏体验
                setTimeout(() => {
                    showRecipeUnlockStory(unlockedRecipe);
                }, 1000);
                
                return true;
            }
            
            return false;
        }
        
        // 显示配方解锁故事
        function showRecipeUnlockStory(recipe) {
            const storyData = testGameData.recipeStories[recipe];
            if (!storyData) {
                log(`❌ 找不到配方 ${recipe} 的故事数据`, 'error');
                return;
            }
            
            log(`📖 显示故事: "${storyData.title}" - ${recipe}`, 'success');
            
            // 创建遮罩层
            const overlay = document.createElement('div');
            overlay.className = 'panel-overlay';
            document.body.appendChild(overlay);
            
            // 创建故事面板
            const storyPanel = document.createElement('div');
            storyPanel.className = 'recipe-unlock-panel';
            storyPanel.innerHTML = `
                <div class="unlock-header">
                    <h3>解锁新配方: ${recipe}</h3>
                    <button class="close-btn">×</button>
                </div>
                <div class="unlock-content">
                    <div class="unlock-image">🍵</div>
                    <div class="unlock-title">${storyData.title}</div>
                    <div class="unlock-story">${storyData.story}</div>
                    <div class="unlock-divider"></div>
                    <div class="unlock-info">
                        <div class="unlock-effect"><b>功效:</b> ${storyData.effect}</div>
                        <div class="unlock-ingredients"><b>材料:</b> ${testGameData.recipeIngredients[recipe].join('、')}</div>
                    </div>
                </div>
                <div class="unlock-footer">
                    <button class="confirm-btn">我已了解</button>
                </div>
            `;
            
            document.body.appendChild(storyPanel);
            
            // 添加关闭功能
            const closePanel = () => {
                storyPanel.remove();
                overlay.remove();
                log(`✅ 故事面板已关闭`, 'info');
            };
            
            storyPanel.querySelector('.close-btn').addEventListener('click', closePanel);
            storyPanel.querySelector('.confirm-btn').addEventListener('click', closePanel);
            overlay.addEventListener('click', closePanel);
        }
        
        // 更新顾客显示
        function updateCustomerDisplay() {
            updateCustomerSection('guaranteed-customers', ['凌小路', '花花', '江飞飞', '江三', '江四']);
            updateCustomerSection('probability-customers', ['池云旗', '江潮', '池惊暮', '江敕封']);
        }
        
        function updateCustomerSection(sectionId, customers) {
            const section = document.getElementById(sectionId);
            section.innerHTML = '';
            
            customers.forEach(customerName => {
                const rule = Object.entries(testGameData.recipeUnlockRules).find(([recipe, rule]) => rule.customer === customerName);
                if (!rule) return;
                
                const [recipeName, ruleData] = rule;
                const visitCount = testGameData.customerVisits[customerName] || 0;
                const isUnlocked = testGameData.unlockedRecipes.includes(recipeName);
                
                const card = document.createElement('div');
                card.className = 'customer-card';
                
                let unlockInfo = '';
                if (ruleData.chance < 1.0) {
                    unlockInfo = `第${ruleData.visitsRequired}次起 ${(ruleData.chance * 100).toFixed(0)}% 概率，第${ruleData.guaranteedOnVisit}次必定解锁`;
                } else {
                    unlockInfo = `第${ruleData.visitsRequired}次必定解锁`;
                }
                
                card.innerHTML = `
                    <div class="customer-name">${customerName}</div>
                    <div class="recipe-name">🍵 ${recipeName}</div>
                    <div class="visit-count">来访次数: ${visitCount}</div>
                    <div class="unlock-status ${isUnlocked ? 'status-unlocked' : 'status-locked'}">
                        ${isUnlocked ? '✅ 已解锁' : '🔒 未解锁'}
                    </div>
                    <div class="visit-info">${unlockInfo}</div>
                    <div class="test-buttons">
                        <button class="test-btn" onclick="testCustomerVisit('${customerName}')">
                            来访一次
                        </button>
                        <button class="test-btn" onclick="testMultipleVisits('${customerName}', 3)">
                            来访3次
                        </button>
                        ${isUnlocked ? 
                            `<button class="test-btn" onclick="showRecipeUnlockStory('${recipeName}')">查看故事</button>` :
                            `<button class="test-btn disabled" disabled>故事未解锁</button>`
                        }
                    </div>
                `;
                
                section.appendChild(card);
            });
        }
        
        // 测试多次来访
        function testMultipleVisits(customerName, times) {
            log(`🔄 开始连续测试 ${customerName} 来访 ${times} 次`, 'info');
            for (let i = 0; i < times; i++) {
                setTimeout(() => {
                    testCustomerVisit(customerName);
                }, i * 500);
            }
        }
        
        // 批量测试所有顾客
        function testAllCustomersOnce() {
            log(`🧪 开始批量测试 - 所有顾客来访1次`, 'info');
            const allCustomers = ['凌小路', '花花', '江飞飞', '江三', '江四', '池云旗', '江潮', '池惊暮', '江敕封'];
            
            allCustomers.forEach((customer, index) => {
                setTimeout(() => {
                    testCustomerVisit(customer);
                }, index * 800);
            });
        }
        
        // 随机显示故事
        function showRandomStory() {
            const allRecipes = Object.keys(testGameData.recipeStories);
            const randomRecipe = allRecipes[Math.floor(Math.random() * allRecipes.length)];
            log(`📖 随机显示故事: ${randomRecipe}`, 'info');
            showRecipeUnlockStory(randomRecipe);
        }
        
        // 重置所有进度
        function resetAllProgress() {
            testGameData.unlockedRecipes = ["五味子饮", "柠檬茶"];
            testGameData.customerVisits = {};
            testGameData.shownRecipeStories = [];
            log(`🔄 所有进度已重置`, 'info');
            updateCustomerDisplay();
        }
        
        // 初始化页面
        function initPage() {
            log(`🎮 特殊顾客解锁流程测试页面已载入`, 'success');
            log(`📊 系统包含 9 种特殊顾客配方解锁机制`, 'info');
            log(`🎯 点击顾客卡片中的按钮开始测试`, 'info');
            updateCustomerDisplay();
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initPage);
    </script>
</body>
</html> 