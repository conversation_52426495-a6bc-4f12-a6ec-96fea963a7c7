<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>茶铺游戏 - 完整测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
            font-size: 28px;
            font-weight: 600;
        }
        
        .test-section {
            background: white;
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 12px;
        }
        
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .test-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        
        .test-btn:active {
            transform: translateY(0);
        }
        
        .test-btn.special {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }
        
        .test-btn.system {
            background: linear-gradient(135deg, #27ae60, #229954);
        }
        
        .test-log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .test-log::-webkit-scrollbar {
            width: 6px;
        }
        
        .test-log::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        
        .test-log::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        
        /* 浮动状态条样式 */
        .floating-status {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 11px;
            line-height: 1.3;
            max-width: 250px;
            cursor: move;
            z-index: 1000;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-family: 'Consolas', monospace;
        }
        
        .floating-status .status-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #ffd700;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            padding-bottom: 3px;
        }
        
        .floating-status .status-item {
            margin: 2px 0;
            font-size: 10px;
        }
        
        .floating-status .unlock-item {
            color: #90EE90;
        }
        
        .floating-status .locked-item {
            color: #FFB6C1;
        }
        
        .floating-status .topping-item {
            color: #87CEEB;
        }
        
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .back-link a {
            color: #3498db;
            text-decoration: none;
            font-weight: 500;
            padding: 10px 20px;
            border: 2px solid #3498db;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        
        .back-link a:hover {
            background: #3498db;
            color: white;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 10px;
            }
            
            .button-grid {
                grid-template-columns: 1fr;
            }
            
            .floating-status {
                max-width: 200px;
                font-size: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍵 茶铺游戏测试中心</h1>
        
        <!-- 基础测试区 -->
        <div class="test-section">
            <div class="section-title">🔧 基础功能测试</div>
            <div class="button-grid">
                <button class="test-btn system" onclick="testBasicCustomer()">生成普通顾客</button>
                <button class="test-btn system" onclick="testAllTeas()">制作所有茶饮</button>
                <button class="test-btn system" onclick="testAllToppings()">添加所有小料</button>
                <button class="test-btn system" onclick="testUnlockProgress()">模拟解锁进度</button>
            </div>
        </div>
        
        <!-- 配方解锁测试区 -->
        <div class="test-section">
            <div class="section-title">🔓 配方解锁测试 (特殊顾客)</div>
            <div class="button-grid">
                <button class="test-btn special" onclick="testSpecialCustomerUnlock('凌小路', '洛神玫瑰饮')">凌小路→洛神玫瑰饮</button>
                <button class="test-btn special" onclick="testSpecialCustomerUnlock('花花', '桂圆红枣茶')">花花→桂圆红枣茶</button>
                <button class="test-btn special" onclick="testSpecialCustomerUnlock('江飞飞', '焦香大麦茶')">江飞飞→焦香大麦茶</button>
                <button class="test-btn special" onclick="testSpecialCustomerUnlock('江三', '三花决明茶')">江三→三花决明茶</button>
                <button class="test-btn special" onclick="testSpecialCustomerUnlock('江四', '薄荷甘草凉茶')">江四→薄荷甘草凉茶</button>
                <button class="test-btn special" onclick="testSpecialCustomerUnlock('池云旗', '陈皮姜米茶')">池云旗→陈皮姜米茶</button>
                <button class="test-btn special" onclick="testSpecialCustomerUnlock('江潮', '冬瓜荷叶饮')">江潮→冬瓜荷叶饮</button>
                <button class="test-btn special" onclick="testSpecialCustomerUnlock('池惊暮', '古法酸梅汤')">池惊暮→古法酸梅汤</button>
                <button class="test-btn special" onclick="testSpecialCustomerUnlock('江敕封', '小吊梨汤')">江敕封→小吊梨汤</button>
            </div>
        </div>
        
        <!-- 人数解锁测试区 -->
        <div class="test-section">
            <div class="section-title">📈 人数解锁测试 (新配方)</div>
            <div class="button-grid">
                <button class="test-btn special" onclick="testCustomerCountUnlock(30, '桑菊润燥茶')">30人→桑菊润燥茶</button>
                <button class="test-btn special" onclick="testCustomerCountUnlock(60, '桂花酒酿饮')">60人→桂花酒酿饮</button>
                <button class="test-btn special" onclick="testCustomerCountUnlock(90, '蜜桃乌龙冷萃')">90人→蜜桃乌龙冷萃</button>
                <button class="test-btn special" onclick="testCustomerCountUnlock(120, '黄芪枸杞茶')">120人→黄芪枸杞茶</button>
                <button class="test-btn special" onclick="testCustomerCountUnlock(150, '竹蔗茅根马蹄水')">150人→竹蔗茅根马蹄水</button>
                <button class="test-btn" onclick="testAllCustomerUnlocks()">解锁所有配方</button>
            </div>
        </div>
        
        <!-- 配方状态检查区 -->
        <div class="test-section">
            <div class="section-title">📋 配方状态检查</div>
            <div class="button-grid">
                <button class="test-btn" onclick="checkAllRecipeStatus()">检查所有配方状态</button>
                <button class="test-btn" onclick="checkSpecialCustomerStatus()">检查特殊顾客访问记录</button>
                <button class="test-btn" onclick="resetUnlockProgress()">重置解锁进度</button>
                <button class="test-btn" onclick="viewRecipeStories()">查看配方故事</button>
            </div>
        </div>
        
        <!-- 系统测试区 -->
        <div class="test-section">
            <div class="section-title">⚙️ 系统测试</div>
            <div class="button-grid">
                <button class="test-btn" onclick="testSaveLoad()">存档加载测试</button>
                <button class="test-btn" onclick="testProcessing()">加工系统测试</button>
                <button class="test-btn" onclick="testShop()">商店功能测试</button>
                <button class="test-btn" onclick="resetGameData()">重置游戏数据</button>
            </div>
        </div>
        
        <!-- 测试日志 -->
        <div class="test-section">
            <div class="section-title">📋 测试日志</div>
            <div class="test-log" id="test-log">
                <div>测试系统已启动，等待执行测试...</div>
            </div>
        </div>
        
        <div class="back-link">
            <a href="index.html">← 返回游戏主页</a>
        </div>
    </div>
    
    <!-- 浮动状态条 -->
    <div class="floating-status" id="floating-status">
        <div class="status-title">🔍 实时状态</div>
        <div id="status-content">
            <div class="status-item">正在加载状态...</div>
        </div>
    </div>

    <script>
        // 获取主游戏的数据
        let gameData = null;
        
        // 初始化
        window.onload = function() {
            loadGameData();
            updateFloatingStatus();
            makeDraggable(document.getElementById('floating-status'));
            setInterval(updateFloatingStatus, 2000); // 每2秒更新状态
        };
        
        // 加载游戏数据
        function loadGameData() {
            try {
                const saved = localStorage.getItem('teaShopSave');
                if (saved) {
                    gameData = JSON.parse(saved);
                    log('✅ 成功加载游戏存档数据');
                } else {
                    log('⚠️ 未找到存档，将使用默认数据');
                    gameData = getDefaultGameData();
                }
            } catch (error) {
                log('❌ 加载存档失败: ' + error.message);
                gameData = getDefaultGameData();
            }
        }
        
        // 获取默认游戏数据
        function getDefaultGameData() {
            return {
                coins: 100,
                servedCustomers: 0,
                unlockedRecipes: ['五味子饮', '柠檬茶'],
                customerVisits: {},
                toppings: {
                    "红糖": 5, "薄荷叶": 5, "姜丝": 5, "柚子丝": 5, "银耳丝": 5, "柠檬片": 5, "蜂蜜": 5,
                    "冰糖": 0, "乌龙茶包": 0, "干桂花": 0, "小圆子": 0, "酒酿": 0, "水蜜桃果肉": 0, "黄芪片": 0
                },
                ingredients: {}
            };
        }
        
        // 更新浮动状态条
        function updateFloatingStatus() {
            if (!gameData) return;
            
            const statusContent = document.getElementById('status-content');
            let html = '';
            
            // 服务顾客数
            html += '<div class="status-item">👑 服务顾客: ' + (gameData.servedCustomers || 0) + '</div>';
            
            // 特殊顾客解锁状态
            const specialCustomerRecipes = [
                { customer: '凌小路', recipe: '洛神玫瑰饮' },
                { customer: '花花', recipe: '桂圆红枣茶' },
                { customer: '江飞飞', recipe: '焦香大麦茶' },
                { customer: '江三', recipe: '三花决明茶' },
                { customer: '江四', recipe: '薄荷甘草凉茶' },
                { customer: '池云旗', recipe: '陈皮姜米茶' },
                { customer: '江潮', recipe: '冬瓜荷叶饮' },
                { customer: '池惊暮', recipe: '古法酸梅汤' },
                { customer: '江敕封', recipe: '小吊梨汤' }
            ];
            
            html += '<div class="status-item" style="margin-top:5px; border-top:1px solid rgba(255,255,255,0.3); padding-top:3px;">👤 特殊顾客配方:</div>';
            specialCustomerRecipes.forEach(item => {
                const isUnlocked = gameData.unlockedRecipes && gameData.unlockedRecipes.includes(item.recipe);
                const visits = gameData.customerVisits ? (gameData.customerVisits[item.customer] || 0) : 0;
                const className = isUnlocked ? 'unlock-item' : 'locked-item';
                const status = isUnlocked ? '✓' : '✗';
                html += `<div class="status-item ${className}">${status} ${item.customer}(${visits}次)</div>`;
            });
            
            // 人数解锁状态
            const unlockConditions = [
                { name: '桑菊润燥茶', requirement: 30 },
                { name: '桂花酒酿饮', requirement: 60 },
                { name: '蜜桃乌龙冷萃', requirement: 90 },
                { name: '黄芪枸杞茶', requirement: 120 },
                { name: '竹蔗茅根马蹄水', requirement: 150 }
            ];
            
            html += '<div class="status-item" style="margin-top:5px; border-top:1px solid rgba(255,255,255,0.3); padding-top:3px;">📈 人数解锁:</div>';
            unlockConditions.forEach(condition => {
                const isUnlocked = (gameData.servedCustomers || 0) >= condition.requirement;
                const className = isUnlocked ? 'unlock-item' : 'locked-item';
                const status = isUnlocked ? '✓' : '✗';
                html += `<div class="status-item ${className}">${status} ${condition.name}(${condition.requirement})</div>`;
            });
            
            // 小料状态
            html += '<div class="status-item" style="margin-top:5px; border-top:1px solid rgba(255,255,255,0.3); padding-top:3px;">🧂 小料库存:</div>';
            const hasAnyToppings = Object.values(gameData.toppings || {}).some(count => count > 0);
            if (hasAnyToppings) {
                Object.entries(gameData.toppings || {}).forEach(([name, count]) => {
                    if (count > 0) {
                        html += `<div class="status-item topping-item">${name}: ${count}</div>`;
                    }
                });
            } else {
                html += '<div class="status-item topping-item">暂无库存</div>';
            }
            
            statusContent.innerHTML = html;
        }
        
        // 测试普通顾客
        function testBasicCustomer() {
            log('🧑‍🤝‍🧑 生成普通顾客测试...');
            if (window.opener && !window.opener.closed) {
                try {
                    window.opener.postMessage({type: 'spawnCustomer'}, '*');
                    log('✅ 已向主窗口发送生成顾客指令');
                } catch (error) {
                    log('❌ 无法与主窗口通信: ' + error.message);
                }
            } else {
                log('⚠️ 主游戏窗口未打开，无法测试');
            }
        }
        
        // 测试特殊顾客解锁
        function testSpecialCustomerUnlock(customerName, recipeName) {
            log(`👑 测试特殊顾客解锁 - ${customerName} → ${recipeName}`);
            
            if (window.opener && !window.opener.closed) {
                try {
                    window.opener.postMessage({
                        type: 'testSpecialCustomerUnlock',
                        customerName: customerName,
                        recipeName: recipeName
                    }, '*');
                    log(`✅ 已发送特殊顾客 ${customerName} 测试指令`);
                } catch (error) {
                    log('❌ 无法与主窗口通信: ' + error.message);
                }
            } else {
                log('⚠️ 主游戏窗口未打开，无法测试');
            }
        }
        
        // 测试人数解锁
        function testCustomerCountUnlock(targetCount, recipeName) {
            log(`📈 测试人数解锁 - ${targetCount}人 → ${recipeName}`);
            
            const oldCount = gameData.servedCustomers || 0;
            gameData.servedCustomers = targetCount;
            saveGameData();
            
            if (window.opener && !window.opener.closed) {
                try {
                    window.opener.postMessage({
                        type: 'updateServedCustomers',
                        count: targetCount
                    }, '*');
                    log(`✅ 服务顾客数已设置为 ${targetCount} (原:${oldCount})`);
                    log(`🔓 检查配方 ${recipeName} 是否解锁...`);
                } catch (error) {
                    log('❌ 无法与主窗口通信: ' + error.message);
                }
            } else {
                log('⚠️ 主游戏窗口未打开，无法测试');
            }
            
            updateFloatingStatus();
        }
        
        // 解锁所有配方
        function testAllCustomerUnlocks() {
            log('🔓 解锁所有配方测试...');
            
            // 设置足够的顾客数量
            gameData.servedCustomers = 200;
            
            // 设置所有特殊顾客访问次数
            if (!gameData.customerVisits) {
                gameData.customerVisits = {};
            }
            
            const specialCustomers = ['凌小路', '花花', '江飞飞', '江三', '江四', '池云旗', '江潮', '池惊暮', '江敕封'];
            specialCustomers.forEach(customer => {
                gameData.customerVisits[customer] = 5; // 设置足够的访问次数
            });
            
            // 添加所有解锁的配方
            const allRecipes = ['洛神玫瑰饮', '桂圆红枣茶', '焦香大麦茶', '三花决明茶', '薄荷甘草凉茶', 
                              '陈皮姜米茶', '冬瓜荷叶饮', '古法酸梅汤', '小吊梨汤',
                              '桑菊润燥茶', '桂花酒酿饮', '蜜桃乌龙冷萃', '黄芪枸杞茶', '竹蔗茅根马蹄水'];
            
            if (!gameData.unlockedRecipes) {
                gameData.unlockedRecipes = [];
            }
            
            allRecipes.forEach(recipe => {
                if (!gameData.unlockedRecipes.includes(recipe)) {
                    gameData.unlockedRecipes.push(recipe);
                }
            });
            
            saveGameData();
            
            if (window.opener && !window.opener.closed) {
                try {
                    window.opener.postMessage({
                        type: 'forceUnlockAllRecipes'
                    }, '*');
                    log('✅ 已强制解锁所有配方');
                } catch (error) {
                    log('❌ 无法与主窗口通信: ' + error.message);
                }
            }
            
            updateFloatingStatus();
            log('✅ 所有配方解锁完成');
        }
        
        // 检查所有配方状态
        function checkAllRecipeStatus() {
            log('📋 检查所有配方状态...');
            
            const allRecipes = {
                // 基础配方
                '五味子饮': '基础配方',
                '柠檬茶': '基础配方',
                // 特殊顾客解锁
                '洛神玫瑰饮': '凌小路',
                '桂圆红枣茶': '花花',
                '焦香大麦茶': '江飞飞',
                '三花决明茶': '江三',
                '薄荷甘草凉茶': '江四',
                '陈皮姜米茶': '池云旗',
                '冬瓜荷叶饮': '江潮',
                '古法酸梅汤': '池惊暮',
                '小吊梨汤': '江敕封',
                // 人数解锁
                '桑菊润燥茶': '30人解锁',
                '桂花酒酿饮': '60人解锁',
                '蜜桃乌龙冷萃': '90人解锁',
                '黄芪枸杞茶': '120人解锁',
                '竹蔗茅根马蹄水': '150人解锁'
            };
            
            Object.entries(allRecipes).forEach(([recipe, unlockType]) => {
                const isUnlocked = gameData.unlockedRecipes && gameData.unlockedRecipes.includes(recipe);
                const status = isUnlocked ? '✅ 已解锁' : '🔒 未解锁';
                log(`${status} ${recipe} (${unlockType})`);
            });
            
            log(`📊 当前服务顾客数: ${gameData.servedCustomers || 0}`);
        }
        
        // 检查特殊顾客状态
        function checkSpecialCustomerStatus() {
            log('👑 检查特殊顾客访问记录...');
            
            const specialCustomers = ['凌小路', '花花', '江飞飞', '江三', '江四', '池云旗', '江潮', '池惊暮', '江敕封'];
            
            if (!gameData.customerVisits) {
                log('⚠️ 没有顾客访问记录');
                return;
            }
            
            specialCustomers.forEach(customer => {
                const visits = gameData.customerVisits[customer] || 0;
                log(`👤 ${customer}: 访问 ${visits} 次`);
            });
        }
        
        // 重置解锁进度
        function resetUnlockProgress() {
            log('🔄 重置解锁进度...');
            
            if (confirm('确定要重置所有解锁进度吗？')) {
                gameData.servedCustomers = 0;
                gameData.unlockedRecipes = ['五味子饮', '柠檬茶']; // 只保留基础配方
                gameData.customerVisits = {};
                
                saveGameData();
                updateFloatingStatus();
                
                if (window.opener && !window.opener.closed) {
                    try {
                        window.opener.postMessage({
                            type: 'resetUnlockProgress'
                        }, '*');
                        log('✅ 已同步重置主窗口解锁进度');
                    } catch (error) {
                        log('❌ 无法与主窗口通信: ' + error.message);
                    }
                }
                
                log('✅ 解锁进度已重置');
            } else {
                log('❌ 取消重置操作');
            }
        }
        
        // 查看配方故事
        function viewRecipeStories() {
            log('📖 查看配方故事...');
            
            const stories = {
                '洛神玫瑰饮': '凌小路的朱砂故事',
                '焦香大麦茶': '江飞飞的雪夜故事',
                '三花决明茶': '江三的夜狩故事',
                '古法酸梅汤': '池惊暮的梅香故事'
            };
            
            Object.entries(stories).forEach(([recipe, story]) => {
                const isUnlocked = gameData.unlockedRecipes && gameData.unlockedRecipes.includes(recipe);
                if (isUnlocked) {
                    log(`📚 ${recipe}: ${story} ✅`);
                } else {
                    log(`📚 ${recipe}: 未解锁 🔒`);
                }
            });
        }
        
        // 测试所有茶饮
        function testAllTeas() {
            log('🍵 制作所有茶饮测试...');
            if (window.opener && !window.opener.closed) {
                try {
                    window.opener.postMessage({type: 'makeAllTeas'}, '*');
                    log('✅ 已向主窗口发送制作所有茶饮指令');
                } catch (error) {
                    log('❌ 无法与主窗口通信: ' + error.message);
                }
            } else {
                log('⚠️ 主游戏窗口未打开，无法测试');
            }
        }
        
        // 测试所有小料
        function testAllToppings() {
            log('🧂 添加所有小料库存...');
            Object.keys(gameData.toppings).forEach(topping => {
                gameData.toppings[topping] = 10;
            });
            saveGameData();
            updateFloatingStatus();
            log('✅ 所有小料库存已设置为10');
        }
        
        // 测试解锁进度
        function testUnlockProgress() {
            log('🔓 模拟解锁进度测试...');
            gameData.servedCustomers = 150; // 解锁所有配方
            gameData.unlockedRecipes = ['桑菊润燥茶', '桂花酒酿饮', '蜜桃乌龙冷萃', '黄芪枸杞茶', '竹蔗茅根马蹄水'];
            saveGameData();
            updateFloatingStatus();
            log('✅ 已解锁所有新配方 (服务顾客数设置为150)');
        }
        
        // 其他测试函数
        function testSaveLoad() {
            log('💾 存档加载测试...');
            try {
                const testData = JSON.stringify(gameData);
                localStorage.setItem('teaShopSave_test', testData);
                const loaded = localStorage.getItem('teaShopSave_test');
                JSON.parse(loaded);
                localStorage.removeItem('teaShopSave_test');
                log('✅ 存档系统工作正常');
            } catch (error) {
                log('❌ 存档系统错误: ' + error.message);
            }
        }
        
        function testProcessing() {
            log('⚙️ 加工系统测试...');
            log('📋 检查加工配方: 甘蔗→红糖x3, 薄荷→薄荷叶x3...');
            log('✅ 加工系统配置正确 (1原料→3成品)');
        }
        
        function testShop() {
            log('🛒 商店功能测试...');
            log('📋 检查新种子: 桑叶(2铜板), 杭白菊(2铜板), 水蜜桃(3铜板)...');
            log('📋 检查新物品: 冰糖(3铜板), 乌龙茶包(4铜板)');
            log('✅ 商店物品配置正确');
        }
        
        function resetGameData() {
            log('🔄 重置游戏数据...');
            if (confirm('确定要重置所有游戏数据吗？这将清除所有进度！')) {
                localStorage.removeItem('teaShopSave');
                gameData = getDefaultGameData();
                updateFloatingStatus();
                log('✅ 游戏数据已重置');
            } else {
                log('❌ 取消重置操作');
            }
        }
        
        // 保存游戏数据
        function saveGameData() {
            try {
                localStorage.setItem('teaShopSave', JSON.stringify(gameData));
            } catch (error) {
                log('❌ 保存数据失败: ' + error.message);
            }
        }
        
        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('test-log');
            const time = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${time}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        // 使元素可拖拽
        function makeDraggable(element) {
            let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
            
            element.onmousedown = dragMouseDown;
            element.ontouchstart = dragTouchStart;
            
            function dragMouseDown(e) {
                e = e || window.event;
                e.preventDefault();
                pos3 = e.clientX;
                pos4 = e.clientY;
                document.onmouseup = closeDragElement;
                document.onmousemove = elementDrag;
            }
            
            function dragTouchStart(e) {
                e = e || window.event;
                e.preventDefault();
                pos3 = e.touches[0].clientX;
                pos4 = e.touches[0].clientY;
                document.ontouchend = closeDragElement;
                document.ontouchmove = elementTouchDrag;
            }
            
            function elementDrag(e) {
                e = e || window.event;
                e.preventDefault();
                pos1 = pos3 - e.clientX;
                pos2 = pos4 - e.clientY;
                pos3 = e.clientX;
                pos4 = e.clientY;
                element.style.top = (element.offsetTop - pos2) + "px";
                element.style.left = (element.offsetLeft - pos1) + "px";
                element.style.right = 'auto';
            }
            
            function elementTouchDrag(e) {
                e = e || window.event;
                e.preventDefault();
                pos1 = pos3 - e.touches[0].clientX;
                pos2 = pos4 - e.touches[0].clientY;
                pos3 = e.touches[0].clientX;
                pos4 = e.touches[0].clientY;
                element.style.top = (element.offsetTop - pos2) + "px";
                element.style.left = (element.offsetLeft - pos1) + "px";
                element.style.right = 'auto';
            }
            
            function closeDragElement() {
                document.onmouseup = null;
                document.onmousemove = null;
                document.ontouchend = null;
                document.ontouchmove = null;
            }
        }
        
        // 监听来自主窗口的消息
        window.addEventListener('message', function(event) {
            if (event.data.type === 'gameDataUpdate') {
                gameData = event.data.gameData;
                updateFloatingStatus();
                log('📡 收到主窗口数据更新');
            }
        });
    </script>
</body>
</html> 