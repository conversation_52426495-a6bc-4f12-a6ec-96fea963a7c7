<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>茶铺游戏 - 功能测试页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            min-height: 100vh;
            padding: 15px;
            color: #333;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            margin-bottom: 25px;
            color: #2c3e50;
            font-size: 24px;
            font-weight: 600;
        }

        .test-section {
            background: #f8f9fa;
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #495057;
            border-left: 3px solid #007bff;
            padding-left: 10px;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 8px;
        }

        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
            text-align: center;
        }

        .test-btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .test-btn:active {
            transform: translateY(0);
        }

        .test-btn.danger {
            background: #dc3545;
        }

        .test-btn.danger:hover {
            background: #c82333;
        }

        .test-btn.success {
            background: #28a745;
        }

        .test-btn.success:hover {
            background: #218838;
        }

        .test-btn.warning {
            background: #ffc107;
            color: #212529;
        }

        .test-btn.warning:hover {
            background: #e0a800;
        }

        .status-panel {
            position: fixed;
            top: 15px;
            right: 15px;
            background: rgba(0, 0, 0, 0.85);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 11px;
            max-width: 220px;
            z-index: 1000;
            font-family: 'Consolas', monospace;
        }

        .status-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #ffd700;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            padding-bottom: 4px;
        }

        .status-item {
            margin: 3px 0;
            font-size: 10px;
        }

        .status-unlocked {
            color: #90EE90;
        }

        .status-locked {
            color: #FFB6C1;
        }

        .log-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 12px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Consolas', monospace;
            font-size: 11px;
            line-height: 1.4;
        }

        .log-panel::-webkit-scrollbar {
            width: 4px;
        }

        .log-panel::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .log-panel::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border: 2px solid #007bff;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .back-link a:hover {
            background: #007bff;
            color: white;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 10px;
            }

            .button-grid {
                grid-template-columns: 1fr;
            }

            .status-panel {
                max-width: 180px;
                font-size: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍵 茶铺功能测试</h1>

        <!-- 快速测试区 -->
        <div class="test-section">
            <div class="section-title">⚡ 快速测试</div>
            <div class="button-grid">
                <button class="test-btn success" onclick="spawnCustomer()">生成顾客</button>
                <button class="test-btn success" onclick="makeAllTeas()">制作所有茶饮</button>
                <button class="test-btn success" onclick="addAllToppings()">添加所有小料</button>
                <button class="test-btn warning" onclick="unlockAllRecipes()">解锁所有配方</button>
            </div>
        </div>

        <!-- 特殊顾客测试 -->
        <div class="test-section">
            <div class="section-title">👑 特殊顾客测试</div>
            <div class="button-grid">
                <button class="test-btn" onclick="testSpecialCustomer('凌小路', '洛神玫瑰饮')">凌小路→洛神玫瑰饮</button>
                <button class="test-btn" onclick="testSpecialCustomer('花花', '桂圆红枣茶')">花花→桂圆红枣茶</button>
                <button class="test-btn" onclick="testSpecialCustomer('江飞飞', '焦香大麦茶')">江飞飞→焦香大麦茶</button>
                <button class="test-btn" onclick="testSpecialCustomer('江三', '三花决明茶')">江三→三花决明茶</button>
                <button class="test-btn" onclick="testSpecialCustomer('江四', '薄荷甘草凉茶')">江四→薄荷甘草凉茶</button>
                <button class="test-btn" onclick="testSpecialCustomer('池云旗', '陈皮姜米茶')">池云旗→陈皮姜米茶</button>
                <button class="test-btn" onclick="testSpecialCustomer('江潮', '冬瓜荷叶饮')">江潮→冬瓜荷叶饮</button>
                <button class="test-btn" onclick="testSpecialCustomer('池惊暮', '古法酸梅汤')">池惊暮→古法酸梅汤</button>
                <button class="test-btn" onclick="testSpecialCustomer('江敕封', '小吊梨汤')">江敕封→小吊梨汤</button>
            </div>
        </div>

        <!-- 人数解锁测试 -->
        <div class="test-section">
            <div class="section-title">📈 人数解锁测试</div>
            <div class="button-grid">
                <button class="test-btn" onclick="setCustomerCount(30)">设置30人→桑菊润燥茶</button>
                <button class="test-btn" onclick="setCustomerCount(60)">设置60人→桂花酒酿饮</button>
                <button class="test-btn" onclick="setCustomerCount(90)">设置90人→蜜桃乌龙冷萃</button>
                <button class="test-btn" onclick="setCustomerCount(120)">设置120人→黄芪枸杞茶</button>
                <button class="test-btn" onclick="setCustomerCount(150)">设置150人→竹蔗茅根马蹄水</button>
            </div>
        </div>

        <!-- 配方状态检查区 -->
        <div class="test-section">
            <div class="section-title">📋 配方状态检查</div>
            <div class="button-grid">
                <button class="test-btn" onclick="checkRecipeStatus()">检查配方状态</button>
                <button class="test-btn" onclick="checkCustomerVisits()">检查顾客访问</button>
                <button class="test-btn danger" onclick="resetProgress()">重置进度</button>
                <button class="test-btn danger" onclick="resetGameData()">重置游戏</button>
            </div>
        </div>

        <!-- 测试日志 -->
        <div class="test-section">
            <div class="section-title">📋 测试日志</div>
            <div class="log-panel" id="test-log">
                <div>测试系统已启动，等待执行测试...</div>
            </div>
        </div>

        <div class="back-link">
            <a href="index.html">← 返回游戏主页</a>
        </div>
    </div>

    <!-- 状态面板 -->
    <div class="status-panel" id="status-panel">
        <div class="status-title">🔍 实时状态</div>
        <div id="status-content">
            <div class="status-item">正在加载状态...</div>
        </div>
    </div>

    <script>
        // 游戏数据
        let gameData = null;

        // 初始化
        window.onload = function() {
            loadGameData();
            updateStatus();
            setInterval(updateStatus, 3000); // 每3秒更新状态
        };

        // 加载游戏数据
        function loadGameData() {
            try {
                const saved = localStorage.getItem('teaShopSave');
                if (saved) {
                    gameData = JSON.parse(saved);
                    log('✅ 成功加载游戏存档');
                } else {
                    log('⚠️ 未找到存档，使用默认数据');
                    gameData = getDefaultGameData();
                }
            } catch (error) {
                log('❌ 加载存档失败: ' + error.message);
                gameData = getDefaultGameData();
            }
        }

        // 获取默认游戏数据
        function getDefaultGameData() {
            return {
                coins: 100,
                servedCustomers: 0,
                unlockedRecipes: ['五味子饮', '柠檬茶'],
                customerVisits: {},
                toppings: {
                    "红糖": 5, "薄荷叶": 5, "姜丝": 5, "柚子丝": 5, "银耳丝": 5, "柠檬片": 5, "蜂蜜": 5,
                    "冰糖": 0, "乌龙茶包": 0, "干桂花": 0, "小圆子": 0, "酒酿": 0, "水蜜桃果肉": 0, "黄芪片": 0
                },
                ingredients: {}
            };
        }

        // 更新状态面板
        function updateStatus() {
            if (!gameData) return;

            const statusContent = document.getElementById('status-content');
            let html = '';

            // 服务顾客数
            html += '<div class="status-item">👑 服务顾客: ' + (gameData.servedCustomers || 0) + '</div>';

            // 特殊顾客解锁状态
            const specialRecipes = [
                { customer: '凌小路', recipe: '洛神玫瑰饮' },
                { customer: '花花', recipe: '桂圆红枣茶' },
                { customer: '江飞飞', recipe: '焦香大麦茶' },
                { customer: '江三', recipe: '三花决明茶' },
                { customer: '江四', recipe: '薄荷甘草凉茶' },
                { customer: '池云旗', recipe: '陈皮姜米茶' },
                { customer: '江潮', recipe: '冬瓜荷叶饮' },
                { customer: '池惊暮', recipe: '古法酸梅汤' },
                { customer: '江敕封', recipe: '小吊梨汤' }
            ];

            html += '<div class="status-item" style="margin-top:5px; border-top:1px solid rgba(255,255,255,0.3); padding-top:3px;">👤 特殊顾客:</div>';
            specialRecipes.forEach(item => {
                const isUnlocked = gameData.unlockedRecipes && gameData.unlockedRecipes.includes(item.recipe);
                const visits = gameData.customerVisits ? (gameData.customerVisits[item.customer] || 0) : 0;
                const className = isUnlocked ? 'status-unlocked' : 'status-locked';
                const status = isUnlocked ? '✓' : '✗';
                html += `<div class="status-item ${className}">${status} ${item.customer}(${visits})</div>`;
            });

            // 人数解锁状态
            const countUnlocks = [
                { name: '桑菊润燥茶', requirement: 30 },
                { name: '桂花酒酿饮', requirement: 60 },
                { name: '蜜桃乌龙冷萃', requirement: 90 },
                { name: '黄芪枸杞茶', requirement: 120 },
                { name: '竹蔗茅根马蹄水', requirement: 150 }
            ];

            html += '<div class="status-item" style="margin-top:5px; border-top:1px solid rgba(255,255,255,0.3); padding-top:3px;">📈 人数解锁:</div>';
            countUnlocks.forEach(condition => {
                const isUnlocked = (gameData.servedCustomers || 0) >= condition.requirement;
                const className = isUnlocked ? 'status-unlocked' : 'status-locked';
                const status = isUnlocked ? '✓' : '✗';
                html += `<div class="status-item ${className}">${status} ${condition.name}(${condition.requirement})</div>`;
            });

            statusContent.innerHTML = html;
        }

        // 生成顾客
        function spawnCustomer() {
            log('🧑‍🤝‍🧑 生成普通顾客...');
            sendToMainWindow({type: 'spawnCustomer'});
        }

        // 制作所有茶饮
        function makeAllTeas() {
            log('🍵 制作所有茶饮...');
            sendToMainWindow({type: 'makeAllTeas'});
        }

        // 添加所有小料
        function addAllToppings() {
            log('🧂 添加所有小料...');
            Object.keys(gameData.toppings).forEach(topping => {
                gameData.toppings[topping] = 10;
            });
            saveGameData();
            updateStatus();
            log('✅ 所有小料库存已设置为10');
        }

        // 解锁所有配方
        function unlockAllRecipes() {
            log('🔓 解锁所有配方...');

            // 设置足够的顾客数量
            gameData.servedCustomers = 200;

            // 设置所有特殊顾客访问次数
            if (!gameData.customerVisits) {
                gameData.customerVisits = {};
            }

            const specialCustomers = ['凌小路', '花花', '江飞飞', '江三', '江四', '池云旗', '江潮', '池惊暮', '江敕封'];
            specialCustomers.forEach(customer => {
                gameData.customerVisits[customer] = 5;
            });

            // 添加所有解锁的配方
            const allRecipes = ['洛神玫瑰饮', '桂圆红枣茶', '焦香大麦茶', '三花决明茶', '薄荷甘草凉茶',
                              '陈皮姜米茶', '冬瓜荷叶饮', '古法酸梅汤', '小吊梨汤',
                              '桑菊润燥茶', '桂花酒酿饮', '蜜桃乌龙冷萃', '黄芪枸杞茶', '竹蔗茅根马蹄水'];

            if (!gameData.unlockedRecipes) {
                gameData.unlockedRecipes = [];
            }

            allRecipes.forEach(recipe => {
                if (!gameData.unlockedRecipes.includes(recipe)) {
                    gameData.unlockedRecipes.push(recipe);
                }
            });

            saveGameData();
            sendToMainWindow({type: 'forceUnlockAllRecipes'});
            updateStatus();
            log('✅ 所有配方解锁完成');
        }

        // 测试特殊顾客
        function testSpecialCustomer(customerName, recipeName) {
            log(`👑 测试特殊顾客 - ${customerName} → ${recipeName}`);
            sendToMainWindow({
                type: 'testSpecialCustomerUnlock',
                customerName: customerName,
                recipeName: recipeName
            });
        }

        // 设置顾客数量
        function setCustomerCount(count) {
            log(`📈 设置服务顾客数为 ${count}...`);
            const oldCount = gameData.servedCustomers || 0;
            gameData.servedCustomers = count;
            saveGameData();
            sendToMainWindow({
                type: 'updateServedCustomers',
                count: count
            });
            updateStatus();
            log(`✅ 服务顾客数已设置为 ${count} (原:${oldCount})`);
        }

        // 检查配方状态
        function checkRecipeStatus() {
            log('📋 检查配方状态...');

            const allRecipes = {
                // 基础配方
                '五味子饮': '基础配方',
                '柠檬茶': '基础配方',
                // 特殊顾客解锁
                '洛神玫瑰饮': '凌小路',
                '桂圆红枣茶': '花花',
                '焦香大麦茶': '江飞飞',
                '三花决明茶': '江三',
                '薄荷甘草凉茶': '江四',
                '陈皮姜米茶': '池云旗',
                '冬瓜荷叶饮': '江潮',
                '古法酸梅汤': '池惊暮',
                '小吊梨汤': '江敕封',
                // 人数解锁
                '桑菊润燥茶': '30人解锁',
                '桂花酒酿饮': '60人解锁',
                '蜜桃乌龙冷萃': '90人解锁',
                '黄芪枸杞茶': '120人解锁',
                '竹蔗茅根马蹄水': '150人解锁'
            };

            Object.entries(allRecipes).forEach(([recipe, unlockType]) => {
                const isUnlocked = gameData.unlockedRecipes && gameData.unlockedRecipes.includes(recipe);
                const status = isUnlocked ? '✅ 已解锁' : '🔒 未解锁';
                log(`${status} ${recipe} (${unlockType})`);
            });

            log(`📊 当前服务顾客数: ${gameData.servedCustomers || 0}`);
        }

        // 检查顾客访问
        function checkCustomerVisits() {
            log('👑 检查特殊顾客访问记录...');

            const specialCustomers = ['凌小路', '花花', '江飞飞', '江三', '江四', '池云旗', '江潮', '池惊暮', '江敕封'];

            if (!gameData.customerVisits) {
                log('⚠️ 没有顾客访问记录');
                return;
            }

            specialCustomers.forEach(customer => {
                const visits = gameData.customerVisits[customer] || 0;
                log(`👤 ${customer}: 访问 ${visits} 次`);
            });
        }

        // 重置进度
        function resetProgress() {
            log('🔄 重置解锁进度...');

            if (confirm('确定要重置所有解锁进度吗？')) {
                gameData.servedCustomers = 0;
                gameData.unlockedRecipes = ['五味子饮', '柠檬茶']; // 只保留基础配方
                gameData.customerVisits = {};

                saveGameData();
                sendToMainWindow({type: 'resetUnlockProgress'});
                updateStatus();
                log('✅ 解锁进度已重置');
            } else {
                log('❌ 取消重置操作');
            }
        }

        // 重置游戏数据
        function resetGameData() {
            log('🔄 重置游戏数据...');
            if (confirm('确定要重置所有游戏数据吗？这将清除所有进度！')) {
                localStorage.removeItem('teaShopSave');
                gameData = getDefaultGameData();
                updateStatus();
                log('✅ 游戏数据已重置');
            } else {
                log('❌ 取消重置操作');
            }
        }

        // 保存游戏数据
        function saveGameData() {
            try {
                localStorage.setItem('teaShopSave', JSON.stringify(gameData));
            } catch (error) {
                log('❌ 保存数据失败: ' + error.message);
            }
        }

        // 向主窗口发送消息
        function sendToMainWindow(data) {
            if (window.opener && !window.opener.closed) {
                try {
                    window.opener.postMessage(data, '*');
                    log('✅ 已发送指令到主窗口');
                } catch (error) {
                    log('❌ 无法与主窗口通信: ' + error.message);
                }
            } else {
                log('⚠️ 主游戏窗口未打开，无法测试');
            }
        }

        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('test-log');
            const time = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${time}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // 监听来自主窗口的消息
        window.addEventListener('message', function(event) {
            if (event.data.type === 'gameDataUpdate') {
                gameData = event.data.gameData;
                updateStatus();
                log('📡 收到主窗口数据更新');
            }
        });
    </script>
</body>
</html>