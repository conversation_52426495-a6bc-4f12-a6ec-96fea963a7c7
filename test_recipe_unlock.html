<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配方解锁系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .unlock-status {
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 3px;
        }
        .unlocked {
            background: #d4edda;
            color: #155724;
        }
        .locked {
            background: #f8d7da;
            color: #721c24;
        }
        .customer-visits {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .customer-card {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🍵 配方解锁系统测试</h1>
        
        <div class="test-section">
            <h2>📊 当前解锁状态</h2>
            <div id="unlock-status"></div>
        </div>
        
        <div class="test-section">
            <h2>👥 顾客访问记录</h2>
            <div id="customer-visits"></div>
        </div>
        
        <div class="test-section">
            <h2>🧪 测试操作</h2>
            <button class="test-button" onclick="testCustomerVisit('凌小路')">模拟凌小路来访</button>
            <button class="test-button" onclick="testCustomerVisit('花花')">模拟花花来访</button>
            <button class="test-button" onclick="testCustomerVisit('江飞飞')">模拟江飞飞来访</button>
            <button class="test-button" onclick="testCustomerVisit('江三')">模拟江三来访</button>
            <button class="test-button" onclick="testCustomerVisit('江四')">模拟江四来访</button>
            <button class="test-button" onclick="testCustomerVisit('池云旗')">模拟池云旗来访</button>
            <button class="test-button" onclick="testCustomerVisit('江潮')">模拟江潮来访</button>
            <button class="test-button" onclick="testCustomerVisit('池惊暮')">模拟池惊暮来访</button>
            <button class="test-button" onclick="testCustomerVisit('江敕封')">模拟江敕封来访</button>
            <br><br>
            <button class="test-button" onclick="resetUnlockSystem()" style="background: #f44336;">重置解锁系统</button>
            <button class="test-button" onclick="unlockAllRecipes()" style="background: #ff9800;">解锁全部配方</button>
        </div>
        
        <div class="test-section">
            <h2>📝 测试日志</h2>
            <div id="test-log" style="height: 200px; overflow-y: auto; background: #f9f9f9; padding: 10px; border-radius: 3px;"></div>
        </div>
    </div>

    <script>
        // 模拟游戏数据结构
        const testGameData = {
            unlockedRecipes: ["五味子饮", "柠檬茶"],
            customerVisits: {},
            recipeUnlockRules: {
                "洛神玫瑰饮": { customer: "凌小路", visitsRequired: 1, chance: 1.0, guaranteedOnVisit: 1 },
                "桂圆红枣茶": { customer: "花花", visitsRequired: 1, chance: 1.0, guaranteedOnVisit: 1 },
                "焦香大麦茶": { customer: "江飞飞", visitsRequired: 2, chance: 1.0, guaranteedOnVisit: 2 },
                "三花决明茶": { customer: "江三", visitsRequired: 2, chance: 1.0, guaranteedOnVisit: 2 },
                "薄荷甘草凉茶": { customer: "江四", visitsRequired: 2, chance: 1.0, guaranteedOnVisit: 2 },
                "陈皮姜米茶": { customer: "池云旗", visitsRequired: 2, chance: 0.5, guaranteedOnVisit: 3 },
                "冬瓜荷叶饮": { customer: "江潮", visitsRequired: 3, chance: 0.6, guaranteedOnVisit: 4 },
                "古法酸梅汤": { customer: "池惊暮", visitsRequired: 2, chance: 0.3, guaranteedOnVisit: 3 },
                "小吊梨汤": { customer: "江敕封", visitsRequired: 3, chance: 0.4, guaranteedOnVisit: 5 },
            },
            recipeStories: {
                "洛神玫瑰饮": { customer: "凌小路", title: "朱砂" },
                "桂圆红枣茶": { customer: "花花", title: "无归" },
                "焦香大麦茶": { customer: "江飞飞", title: "雪夜" },
                "三花决明茶": { customer: "江三", title: "夜狩" },
                "薄荷甘草凉茶": { customer: "江四", title: "三哥" },
                "陈皮姜米茶": { customer: "池云旗", title: "师徒" },
                "冬瓜荷叶饮": { customer: "江潮", title: "师徒2" },
                "古法酸梅汤": { customer: "池惊暮", title: "梅香" },
                "小吊梨汤": { customer: "江敕封", title: "琴心" }
            }
        };

        function log(message) {
            const logDiv = document.getElementById('test-log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function checkRecipeUnlock(customerName) {
            log(`检查顾客 ${customerName} 的配方解锁条件`);
            
            // 增加来访次数
            if (!testGameData.customerVisits[customerName]) {
                testGameData.customerVisits[customerName] = 1;
            } else {
                testGameData.customerVisits[customerName]++;
            }
            
            const visitCount = testGameData.customerVisits[customerName];
            log(`${customerName} 已来访 ${visitCount} 次`);
            
            // 检查是否有配方可以解锁
            let unlockedRecipe = null;
            
            Object.entries(testGameData.recipeUnlockRules).forEach(([recipe, rule]) => {
                if (rule.customer === customerName) {
                    if (testGameData.unlockedRecipes.includes(recipe)) {
                        return;
                    }
                    
                    if (visitCount >= rule.visitsRequired) {
                        let shouldUnlock = false;
                        
                        if (visitCount >= rule.guaranteedOnVisit) {
                            shouldUnlock = true;
                        } else if (Math.random() < rule.chance) {
                            shouldUnlock = true;
                        }
                        
                        if (shouldUnlock) {
                            unlockedRecipe = recipe;
                        }
                    }
                }
            });
            
            if (unlockedRecipe) {
                log(`🎉 解锁配方: ${unlockedRecipe}`);
                testGameData.unlockedRecipes.push(unlockedRecipe);
                showStoryPreview(unlockedRecipe);
                return true;
            }
            
            return false;
        }

        function showStoryPreview(recipe) {
            const story = testGameData.recipeStories[recipe];
            if (story) {
                log(`📖 故事预览: "${story.title}" - ${recipe}`);
            }
        }

        function testCustomerVisit(customerName) {
            log(`🏃 ${customerName} 来到茶铺`);
            const unlocked = checkRecipeUnlock(customerName);
            if (!unlocked) {
                log(`❌ 暂未解锁新配方`);
            }
            updateDisplay();
        }

        function resetUnlockSystem() {
            testGameData.unlockedRecipes = ["五味子饮", "柠檬茶"];
            testGameData.customerVisits = {};
            log("🔄 解锁系统已重置");
            updateDisplay();
        }

        function unlockAllRecipes() {
            testGameData.unlockedRecipes = [
                "五味子饮", "柠檬茶", "洛神玫瑰饮", "桂圆红枣茶", 
                "焦香大麦茶", "三花决明茶", "薄荷甘草凉茶", 
                "陈皮姜米茶", "冬瓜荷叶饮", "古法酸梅汤", "小吊梨汤"
            ];
            log("✅ 所有配方已解锁");
            updateDisplay();
        }

        function updateDisplay() {
            updateUnlockStatus();
            updateCustomerVisits();
        }

        function updateUnlockStatus() {
            const statusDiv = document.getElementById('unlock-status');
            const allRecipes = Object.keys(testGameData.recipeUnlockRules);
            allRecipes.unshift("五味子饮", "柠檬茶"); // 添加初始配方
            
            statusDiv.innerHTML = '';
            allRecipes.forEach(recipe => {
                const div = document.createElement('div');
                div.className = 'unlock-status';
                
                const isUnlocked = testGameData.unlockedRecipes.includes(recipe);
                if (isUnlocked) {
                    div.classList.add('unlocked');
                    div.innerHTML = `✅ ${recipe} - 已解锁`;
                } else {
                    div.classList.add('locked');
                    const rule = testGameData.recipeUnlockRules[recipe];
                    if (rule) {
                        const visits = testGameData.customerVisits[rule.customer] || 0;
                        div.innerHTML = `🔒 ${recipe} - 需要${rule.customer}来访${rule.visitsRequired}次 (当前${visits}次)`;
                    }
                }
                statusDiv.appendChild(div);
            });
        }

        function updateCustomerVisits() {
            const visitsDiv = document.getElementById('customer-visits');
            const customers = ['凌小路', '花花', '江飞飞', '江三', '江四', '池云旗', '江潮', '池惊暮', '江敕封'];
            
            visitsDiv.innerHTML = '';
            customers.forEach(customer => {
                const div = document.createElement('div');
                div.className = 'customer-card';
                const visits = testGameData.customerVisits[customer] || 0;
                div.innerHTML = `<strong>${customer}</strong><br>来访 ${visits} 次`;
                visitsDiv.appendChild(div);
            });
        }

        // 初始化显示
        updateDisplay();
        log("🎮 配方解锁系统测试页面已加载");
    </script>
</body>
</html> 