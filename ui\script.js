// 🍵 可爱茶铺 - 完整游戏脚本
console.log('🍵 可爱茶铺启动中...');

// 游戏数据对象
const gameData = {
    // 季节和天气
    currentSeason: "春天",
    currentWeather: "晴天",
    currentDay: 1,
    seasons: ["春天", "夏天", "秋天", "冬天"],
    weathers: ["晴天", "刮风", "下雨", "下雪", "阴天"],
    weatherDuration: 20000, // 毫秒
    weatherStartTime: Date.now(),
    daysInSeason: 0,
    daysPerSeason: 10,

    // 种子和库存
    seeds: {},
    seedInfo: {}, // 存储种子的详细信息
    inventory: {},
    selectedSeedType: null,

    // 农田
    plots: [
        {
            id: 0,
            state: 'empty',
            growthStage: 0,
            stageStartTime: 0,
            moisture: 50,
            fertility: 50,
            plantType: null
        },
        {
            id: 1,
            state: 'empty',
            growthStage: 0,
            stageStartTime: 0,
            moisture: 50,
            fertility: 50,
            plantType: null
        },
        {
            id: 2,
            state: 'empty',
            growthStage: 0,
            stageStartTime: 0,
            moisture: 50,
            fertility: 50,
            plantType: null
        },
        {
            id: 3,
            state: 'empty',
            growthStage: 0,
            stageStartTime: 0,
            moisture: 50,
            fertility: 50,
            plantType: null
        }
    ],
    growthStages: ["长芽", "大叶子", "开花", "成熟"],
    stageDuration: 15000, // 毫秒，每个阶段15秒
    moistureConsumption: 10,
    fertilityConsumption: 5,
    minMoisture: 10,
    minFertility: 20,

    // 炉灶
    stoves: [
        {
            state: 'empty',
            startTime: 0,
            boilDuration: 20000, // 20秒
            recipe: null
        },
        {
            state: 'empty',
            startTime: 0,
            boilDuration: 20000, // 20秒
            recipe: null
        }
    ],

    // 茶饮
    madeTeas: [],
    teaTemps: {},
    teaMakeTimes: {},
    teaCoolingDuration: 20000,

    // 小料
    toppings: {
        "红糖": 5,
        "薄荷叶": 5,
        "姜丝": 5,
        "柚子丝": 5,
        "银耳丝": 5,
        "柠檬片": 5,
        "蜂蜜": 5,
        // 新增小料（初始为0，需要通过加工获得）
        "冰糖": 0,
        "乌龙茶包": 0,
        "干桂花": 0,
        "小圆子": 0,
        "酒酿": 0,
        "水蜜桃果肉": 0,
        "黄芪片": 0
    },

    // 顾客
    customer: {
        active: false,
        name: "暂无顾客",
        isVIP: false,
        teaChoice: null,
        toppingChoices: [],
        arrivalTime: 0,
        patience: 120000, // 普通顾客120秒
        maxPatience: 120000,
        served: false
    },
    customerSpawnCooldown: 5000, // 5秒检查一次是否生成新顾客
    lastCustomerTime: 0,
    customerNames: ['池惊暮', '凌小路', '江飞飞', '江三', '江四', '池云旗', '江潮', '江敕封', '花花', '姬别情', '池九信', '狸怒'],

    // 集卡系统
    collectedCards: {},

    // 消息
    messages: ["欢迎来到可爱茶铺!"],

    // 小篮子选择相关
    selectedPlotForPlanting: null,
    selectedSeedForPlanting: null,

    // 购物车
    cart: [],

    // 货币
    coins: 100,

    // 配方解锁系统
    unlockedRecipes: ["五味子饮", "柠檬茶"],
    customerVisits: {},
    servedCustomers: 0,

    // 配方解锁规则
    recipeUnlockRules: {
        "洛神玫瑰饮": { customer: "凌小路", visitsRequired: 2, chance: 0.3, guaranteedOnVisit: 4 },
        "桂圆红枣茶": { customer: "花花", visitsRequired: 2, chance: 0.3, guaranteedOnVisit: 4 },
        "焦香大麦茶": { customer: "江飞飞", visitsRequired: 2, chance: 0.3, guaranteedOnVisit: 4 },
        "三花决明茶": { customer: "江三", visitsRequired: 2, chance: 0.3, guaranteedOnVisit: 4 },
        "薄荷甘草凉茶": { customer: "江四", visitsRequired: 2, chance: 0.3, guaranteedOnVisit: 4 },
        "陈皮姜米茶": { customer: "池云旗", visitsRequired: 2, chance: 0.3, guaranteedOnVisit: 4 },
        "冬瓜荷叶饮": { customer: "江潮", visitsRequired: 2, chance: 0.3, guaranteedOnVisit: 4 },
        "古法酸梅汤": { customer: "池惊暮", visitsRequired: 2, chance: 0.3, guaranteedOnVisit: 4 },
        "小吊梨汤": { customer: "江敕封", visitsRequired: 3, chance: 0.4, guaranteedOnVisit: 5 },
    },

    // 加工配方
    processingRecipes: {
        '红糖': { ingredients: ['甘蔗'], time: 10000, output: 3 },
        '薄荷叶': { ingredients: ['薄荷'], time: 10000, output: 3 },
        '姜丝': { ingredients: ['生姜'], time: 10000, output: 3 },
        '柚子丝': { ingredients: ['柚子'], time: 10000, output: 3 },
        '银耳丝': { ingredients: ['银耳'], time: 15000, output: 3 },
        '柠檬片': { ingredients: ['柠檬'], time: 10000, output: 3 },
        // 新增加工配方
        '水蜜桃果肉': { ingredients: ['水蜜桃'], time: 12000, output: 3 },
        '黄芪片': { ingredients: ['黄芪'], time: 12000, output: 3 },
        '干桂花': { ingredients: ['桂花'], time: 10000, output: 3 },
        '小圆子': { ingredients: ['糯米'], time: 15000, output: 3 },
        '酒酿': { ingredients: ['米'], time: 18000, output: 3 }
    },

    // 加工台
    processingBoard: {
        state: 'idle', // idle, processing
        recipe: null,
        startTime: 0,
        duration: 0
    },

    // 商店物品
    shopItems: {
        '蜂蜜': { price: 3 },
        '银耳': { price: 3 },
        '红糖': { price: 2 },
        '薄荷叶': { price: 2 },
        // 新增物品
        '冰糖': { price: 3 },
        '乌龙茶包': { price: 4 }
    },

    // 当前活动标签
    activeTab: 'farm',
    // 当前信息滑块索引
    currentSlide: 0
};

// 初始化材料
const MATERIALS = [
    "五味子", "乌梅", "山楂", "陈皮", "甘草", "桂花", "大麦",
    "菊花", "金银花", "决明子", "枸杞", "生姜", "桂圆", "红枣",
    "薄荷", "玫瑰花", "洛神花", "冬瓜", "荷叶", "薏米", "雪花梨",
    "话梅", "甘蔗", "柚子", "柠檬",
    // 新增种子
    "桑叶", "杭白菊", "水蜜桃", "黄芪", "白茅根", "马蹄", "糯米", "米"
];

// 初始化种子和库存
MATERIALS.forEach(material => {
    // 将种子信息存储在单独的对象中
    gameData.seedInfo[material] = {
        price: 1,
        growTime: 30000,
        yield: material
    };
    // 种子数量初始化为0
    gameData.seeds[material] = 0;
    gameData.inventory[material] = 1; // 每种材料初始化为1个
});

// 游戏状态
let isPaused = false;
let isTestMode = false;

// 调试函数
function debug(message) {
    console.log(`[可爱茶铺] ${message}`);
}

// 添加消息函数
function addMessage(message) {
    gameData.messages.push(message);
    if (gameData.messages.length > 10) {
        gameData.messages.shift(); // 保持最多10条消息
    }
    updateMessageDisplay();
}

// 更新消息显示
function updateMessageDisplay() {
    const messageText = document.getElementById('message-text');
    if (messageText && gameData.messages.length > 0) {
        messageText.textContent = gameData.messages[gameData.messages.length - 1];

        // 显示消息气泡
        const messageBubble = document.getElementById('message-bubble');
        if (messageBubble) {
            messageBubble.classList.remove('hidden');

            // 3秒后隐藏消息
            setTimeout(() => {
                messageBubble.classList.add('hidden');
            }, 3000);
        }
    }
}

// 页面加载完成后初始化游戏
document.addEventListener('DOMContentLoaded', function() {
    debug('DOM加载完成，开始初始化游戏...');
    initGame();
});

// 游戏初始化函数
function initGame() {
    debug('初始化游戏...');

    // 注册Service Worker
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('./service-worker.js')
            .then(registration => {
                debug('Service Worker 注册成功');
            })
            .catch(error => {
                debug('Service Worker 注册失败: ' + error);
            });
    }

    // 初始化界面
    initUI();

    // 初始化事件监听器
    initEventListeners();

    // 更新所有显示
    updateAllDisplays();

    // 启动游戏循环
    startGameLoop();

    // 显示欢迎消息
    addMessage('🍵 欢迎来到可爱茶铺！开始您的经营之旅吧～');

    debug('游戏初始化完成！');
}

// 初始化UI界面
function initUI() {
    debug('初始化UI界面...');

    // 初始化农场网格
    initFarmGrid();

    // 初始化厨房区域
    initKitchen();

    // 初始化茶摊区域
    initTeaShop();

    // 初始化小料网格
    initToppingsGrid();

    debug('UI界面初始化完成');
}

// 初始化农场网格
function initFarmGrid() {
    const farmGrid = document.getElementById('farm-grid');
    if (!farmGrid) return;

    farmGrid.innerHTML = '';

    gameData.plots.forEach((plot, index) => {
        const plotCard = createPlotCard(plot, index);
        farmGrid.appendChild(plotCard);
    });
}

// 创建田地卡片
function createPlotCard(plot, index) {
    const plotCard = document.createElement('div');
    plotCard.className = `plot-card ${plot.state === 'empty' ? 'empty' : ''}`;
    plotCard.dataset.plotId = index;

    plotCard.innerHTML = `
        <div class="plot-header">
            <span class="plot-number">${index + 1}</span>
            <button class="plot-action" data-plot="${index}">
                ${getPlotActionText(plot)}
            </button>
        </div>
        <div class="plot-visual">
            <div class="soil"></div>
            ${getPlotVisualContent(plot)}
        </div>
        <div class="plot-stats">
            <div class="stat">
                <span class="stat-icon">💧</span>
                <span class="stat-value">${plot.moisture}%</span>
            </div>
            <div class="stat">
                <span class="stat-icon">🌿</span>
                <span class="stat-value">${plot.fertility}%</span>
            </div>
        </div>
        ${getPlotTimerContent(plot)}
    `;

    return plotCard;
}

// 获取田地操作按钮文本
function getPlotActionText(plot) {
    switch (plot.state) {
        case 'empty':
            return '🌰 种植';
        case 'growing':
            return '🌱 生长中';
        case 'mature':
            return '🎉 收获';
        default:
            return '➕ 种植';
    }
}

// 获取田地视觉内容
function getPlotVisualContent(plot) {
    if (plot.state === 'empty') {
        return '<div class="empty-hint">点击种植</div>';
    } else if (plot.state === 'growing') {
        const stageEmojis = ['🌱', '🌿', '🌸', '🌺'];
        const emoji = stageEmojis[plot.growthStage] || '🌱';
        return `<div class="plant growing">${emoji}</div>`;
    } else if (plot.state === 'mature') {
        return '<div class="plant mature">🌺</div>';
    }
    return '';
}

// 获取田地计时器内容
function getPlotTimerContent(plot) {
    if (plot.state === 'growing') {
        return '<div class="plot-timer">⏰ 生长中...</div>';
    } else if (plot.state === 'mature') {
        return '<div class="plot-ready">✨ 可以收获了！</div>';
    }
    return '<div class="plot-timer"></div>';
}

// 初始化厨房区域
function initKitchen() {
    const stovesGrid = document.getElementById('stoves-grid');
    if (!stovesGrid) return;

    stovesGrid.innerHTML = '';

    gameData.stoves.forEach((stove, index) => {
        const stoveCard = createStoveCard(stove, index);
        stovesGrid.appendChild(stoveCard);
    });

    // 初始化加工台
    initProcessingBoard();
}

// 创建炉灶卡片
function createStoveCard(stove, index) {
    const stoveCard = document.createElement('div');
    stoveCard.className = `stove-card ${stove.state === 'empty' ? 'empty' : stove.state}`;
    stoveCard.dataset.stoveId = index;

    if (stove.state === 'empty') {
        stoveCard.innerHTML = `
            <div class="stove-visual">
                <div class="pot empty">🫖</div>
            </div>
            <div class="stove-info">
                <div class="empty-text">点击制茶</div>
            </div>
            <button class="start-cooking" data-stove="${index}">开始制茶</button>
        `;
    } else if (stove.state === 'cooking') {
        const progress = getStoveProgress(stove);
        stoveCard.innerHTML = `
            <div class="stove-visual">
                <div class="fire">🔥</div>
                <div class="pot">🫖</div>
                <div class="steam">💨</div>
            </div>
            <div class="stove-info">
                <div class="recipe-name">${stove.recipe || '制茶中'}</div>
                <div class="cooking-timer">⏰ 制作中...</div>
            </div>
            <div class="cooking-progress">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${progress}%"></div>
                </div>
            </div>
        `;
    }

    return stoveCard;
}

// 获取炉灶进度
function getStoveProgress(stove) {
    if (stove.state !== 'cooking') return 0;

    const elapsed = Date.now() - stove.startTime;
    const progress = Math.min((elapsed / stove.boilDuration) * 100, 100);
    return Math.round(progress);
}

// 初始化加工台
function initProcessingBoard() {
    const processingBoard = document.getElementById('processing-board');
    if (!processingBoard) return;

    updateProcessingBoard();
    initProcessingRecipes();
}

// 更新加工台显示
function updateProcessingBoard() {
    const processingInfo = document.getElementById('processing-info');
    const processingIngredients = document.getElementById('processing-ingredients');

    if (!processingInfo || !processingIngredients) return;

    if (gameData.processingBoard.state === 'idle') {
        processingInfo.innerHTML = '<div class="processing-status">点击选择加工配方</div>';
        processingIngredients.textContent = '🥕🌿';
    } else if (gameData.processingBoard.state === 'processing') {
        const elapsed = Date.now() - gameData.processingBoard.startTime;
        const remaining = Math.max(0, gameData.processingBoard.duration - elapsed);
        const seconds = Math.ceil(remaining / 1000);

        processingInfo.innerHTML = `
            <div class="processing-status">正在加工 ${gameData.processingBoard.recipe}</div>
            <div class="processing-timer">⏰ ${seconds}秒</div>
        `;
        processingIngredients.textContent = '⚙️🔄';
    }
}

// 初始化加工配方
function initProcessingRecipes() {
    const processingRecipes = document.getElementById('processing-recipes');
    if (!processingRecipes) return;

    processingRecipes.innerHTML = '';

    Object.keys(gameData.processingRecipes).forEach(recipeName => {
        const recipe = gameData.processingRecipes[recipeName];
        const button = document.createElement('button');
        button.className = 'recipe-chip';
        button.textContent = `${recipe.ingredients[0]}→${recipeName}`;
        button.dataset.recipe = recipeName;

        // 检查是否有足够的材料
        const hasIngredients = recipe.ingredients.every(ingredient =>
            gameData.inventory[ingredient] && gameData.inventory[ingredient] > 0
        );

        if (!hasIngredients) {
            button.classList.add('disabled');
        }

        processingRecipes.appendChild(button);
    });
}

// 初始化茶摊区域
function initTeaShop() {
    updateTeaDisplay();
}

// 更新茶饮显示
function updateTeaDisplay() {
    const teaDisplay = document.getElementById('tea-display');
    if (!teaDisplay) return;

    teaDisplay.innerHTML = '';

    if (gameData.madeTeas.length === 0) {
        teaDisplay.innerHTML = `
            <div class="no-tea-hint">
                <span class="hint-icon">🫖</span>
                <span class="hint-text">还没有制作好的茶饮哦～</span>
            </div>
        `;
        return;
    }

    gameData.madeTeas.forEach((tea, index) => {
        const teaItem = createTeaItem(tea, index);
        teaDisplay.appendChild(teaItem);
    });
}

// 创建茶饮项目
function createTeaItem(tea, index) {
    const teaItem = document.createElement('div');
    teaItem.className = 'tea-item';
    teaItem.dataset.teaId = tea.id;

    const isHot = gameData.teaTemps[tea.id] === 'hot';
    const tempClass = isHot ? 'hot' : 'cold';
    const tempIcon = isHot ? '🔥' : '❄️';
    const tempText = isHot ? '热茶' : '冰茶';
    const effectIcon = isHot ? '✨' : '❄️';

    teaItem.innerHTML = `
        <div class="tea-visual">
            <div class="tea-cup">${isHot ? '🍵' : '🧊'}</div>
            <div class="${isHot ? 'steam-effect' : 'ice-effect'}">${effectIcon}</div>
        </div>
        <div class="tea-info">
            <div class="tea-name">${tea.name}</div>
            <div class="tea-temp ${tempClass}">${tempText} ${tempIcon}</div>
        </div>
        <div class="tea-actions">
            <button class="serve-button" data-tea-index="${index}">🎉 服务顾客</button>
            <button class="add-topping" data-tea-index="${index}">➕ 加料</button>
        </div>
    `;

    return teaItem;
}

// 初始化小料网格
function initToppingsGrid() {
    updateToppingsDisplay();
}

// 更新小料显示
function updateToppingsDisplay() {
    const toppingsGrid = document.getElementById('toppings-grid');
    if (!toppingsGrid) return;

    toppingsGrid.innerHTML = '';

    Object.entries(gameData.toppings).forEach(([toppingName, count]) => {
        if (count > 0) { // 只显示有库存的小料
            const toppingItem = createToppingItem(toppingName, count);
            toppingsGrid.appendChild(toppingItem);
        }
    });
}

// 创建小料项目
function createToppingItem(toppingName, count) {
    const toppingItem = document.createElement('div');
    toppingItem.className = 'topping-item';
    toppingItem.dataset.topping = toppingName;

    const toppingIcon = getToppingIcon(toppingName);

    toppingItem.innerHTML = `
        <span class="topping-icon">${toppingIcon}</span>
        <span class="topping-name">${toppingName}</span>
        <span class="topping-count">x${count}</span>
    `;

    return toppingItem;
}

// 获取小料图标
function getToppingIcon(toppingName) {
    const icons = {
        '红糖': '🟤',
        '薄荷叶': '🌿',
        '姜丝': '🫚',
        '柚子丝': '🍊',
        '银耳丝': '🤍',
        '柠檬片': '🍋',
        '蜂蜜': '🍯',
        '冰糖': '🧊',
        '乌龙茶包': '🍃',
        '干桂花': '🌼',
        '小圆子': '⚪',
        '酒酿': '🍶',
        '水蜜桃果肉': '🍑',
        '黄芪片': '🟡'
    };
    return icons[toppingName] || '🌿';
}

// 初始化事件监听器
function initEventListeners() {
    debug('初始化事件监听器...');

    // 选项卡切换
    initTabSwitching();

    // 菜单按钮
    initMenuButton();

    // 农场相关事件
    initFarmEvents();

    // 厨房相关事件
    initKitchenEvents();

    // 茶摊相关事件
    initTeaShopEvents();

    // 购物车按钮
    initCartButton();

    debug('事件监听器初始化完成');
}

// 初始化选项卡切换
function initTabSwitching() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.dataset.tab;

            // 移除所有活动状态
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // 添加活动状态
            button.classList.add('active');
            const targetContent = document.getElementById(targetTab);
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // 更新游戏数据
            gameData.activeTab = targetTab;

            // 刷新当前选项卡的显示
            refreshCurrentTab(targetTab);
        });
    });
}

// 刷新当前选项卡
function refreshCurrentTab(tabName) {
    switch (tabName) {
        case 'farm':
            initFarmGrid();
            break;
        case 'kitchen':
            initKitchen();
            break;
        case 'shop':
            updateTeaDisplay();
            updateToppingsDisplay();
            break;
    }
}

// 初始化菜单按钮
function initMenuButton() {
    const menuButton = document.getElementById('menu-button');
    const menuPanel = document.getElementById('menu-panel');
    const closeMenu = document.getElementById('close-menu');

    if (menuButton && menuPanel) {
        menuButton.addEventListener('click', () => {
            menuPanel.style.display = 'block';
        });
    }

    if (closeMenu && menuPanel) {
        closeMenu.addEventListener('click', () => {
            menuPanel.style.display = 'none';
        });
    }

    // 菜单项事件
    initMenuItems();
}

// 初始化菜单项
function initMenuItems() {
    const saveGameBtn = document.getElementById('save-game-btn');
    const loadGameBtn = document.getElementById('load-game-btn');
    const recipeBookBtn = document.getElementById('recipe-book-btn');
    const testModeBtn = document.getElementById('test-mode-btn');

    if (saveGameBtn) {
        saveGameBtn.addEventListener('click', () => {
            saveGame();
            addMessage('💾 游戏已保存');
        });
    }

    if (loadGameBtn) {
        loadGameBtn.addEventListener('click', () => {
            loadGame();
            addMessage('📁 游戏已加载');
        });
    }

    if (recipeBookBtn) {
        recipeBookBtn.addEventListener('click', () => {
            showRecipeBook();
        });
    }

    if (testModeBtn) {
        testModeBtn.addEventListener('click', () => {
            showTestPanel();
        });
    }
}

// 初始化农场事件
function initFarmEvents() {
    // 田地点击事件
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('plot-action')) {
            const plotIndex = parseInt(e.target.dataset.plot);
            handlePlotAction(plotIndex);
        }
    });

    // 快捷操作按钮
    const waterAllBtn = document.getElementById('water-all-btn');
    const fertilizeAllBtn = document.getElementById('fertilize-all-btn');
    const basketBtn = document.getElementById('basket-btn');

    if (waterAllBtn) {
        waterAllBtn.addEventListener('click', () => {
            waterAllPlots();
        });
    }

    if (fertilizeAllBtn) {
        fertilizeAllBtn.addEventListener('click', () => {
            fertilizeAllPlots();
        });
    }

    if (basketBtn) {
        basketBtn.addEventListener('click', () => {
            showBasketPanel();
        });
    }
}

// 处理田地操作
function handlePlotAction(plotIndex) {
    const plot = gameData.plots[plotIndex];

    if (plot.state === 'empty') {
        // 种植操作
        showBasketPanel(plotIndex);
    } else if (plot.state === 'mature') {
        // 收获操作
        harvestPlot(plotIndex);
    }
}

// 收获田地
function harvestPlot(plotIndex) {
    const plot = gameData.plots[plotIndex];

    if (plot.state !== 'mature') return;

    const harvestedItem = plot.plantType;
    const harvestAmount = Math.floor(Math.random() * 3) + 2; // 2-4个

    // 添加到库存
    if (!gameData.inventory[harvestedItem]) {
        gameData.inventory[harvestedItem] = 0;
    }
    gameData.inventory[harvestedItem] += harvestAmount;

    // 重置田地
    plot.state = 'empty';
    plot.growthStage = 0;
    plot.stageStartTime = 0;
    plot.plantType = null;
    plot.moisture = Math.max(plot.moisture - 20, 0);
    plot.fertility = Math.max(plot.fertility - 15, 0);

    addMessage(`🎉 收获了 ${harvestAmount} 个 ${harvestedItem}`);

    // 刷新显示
    initFarmGrid();
}

// 浇水所有田地
function waterAllPlots() {
    let wateredCount = 0;

    gameData.plots.forEach(plot => {
        if (plot.moisture < 100) {
            plot.moisture = Math.min(plot.moisture + 30, 100);
            wateredCount++;
        }
    });

    if (wateredCount > 0) {
        addMessage(`💧 为 ${wateredCount} 块田地浇了水`);
        initFarmGrid();
    } else {
        addMessage('💧 所有田地的湿度都很充足');
    }
}

// 施肥所有田地
function fertilizeAllPlots() {
    let fertilizedCount = 0;

    gameData.plots.forEach(plot => {
        if (plot.fertility < 100) {
            plot.fertility = Math.min(plot.fertility + 25, 100);
            fertilizedCount++;
        }
    });

    if (fertilizedCount > 0) {
        addMessage(`🌿 为 ${fertilizedCount} 块田地施了肥`);
        initFarmGrid();
    } else {
        addMessage('🌿 所有田地的肥力都很充足');
    }
}

// 初始化厨房事件
function initKitchenEvents() {
    // 炉灶点击事件
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('start-cooking')) {
            const stoveIndex = parseInt(e.target.dataset.stove);
            startCooking(stoveIndex);
        }
    });

    // 加工配方点击事件
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('recipe-chip') && !e.target.classList.contains('disabled')) {
            const recipeName = e.target.dataset.recipe;
            startProcessing(recipeName);
        }
    });
}

// 开始制茶
function startCooking(stoveIndex) {
    const stove = gameData.stoves[stoveIndex];

    if (stove.state !== 'empty') return;

    // 选择一个可制作的配方
    const availableRecipes = gameData.unlockedRecipes.filter(recipe => {
        return canMakeRecipe(recipe);
    });

    if (availableRecipes.length === 0) {
        addMessage('❌ 没有可制作的茶饮配方');
        return;
    }

    // 随机选择一个配方
    const selectedRecipe = availableRecipes[Math.floor(Math.random() * availableRecipes.length)];

    // 消耗材料
    const recipeIngredients = getRecipeIngredients(selectedRecipe);
    recipeIngredients.forEach(ingredient => {
        gameData.inventory[ingredient]--;
    });

    // 开始制茶
    stove.state = 'cooking';
    stove.recipe = selectedRecipe;
    stove.startTime = Date.now();

    addMessage(`🔥 开始制作 ${selectedRecipe}`);

    // 刷新显示
    initKitchen();
}

// 检查是否可以制作配方
function canMakeRecipe(recipeName) {
    const ingredients = getRecipeIngredients(recipeName);
    return ingredients.every(ingredient =>
        gameData.inventory[ingredient] && gameData.inventory[ingredient] > 0
    );
}

// 获取配方所需材料
function getRecipeIngredients(recipeName) {
    const recipeIngredients = {
        '五味子饮': ['五味子', '乌梅', '山楂'],
        '柠檬茶': ['柠檬', '蜂蜜'],
        '洛神玫瑰饮': ['洛神花', '玫瑰花'],
        '桂圆红枣茶': ['桂圆', '红枣'],
        '焦香大麦茶': ['大麦'],
        '三花决明茶': ['菊花', '金银花', '决明子'],
        '薄荷甘草凉茶': ['薄荷', '甘草'],
        '陈皮姜米茶': ['陈皮', '生姜'],
        '冬瓜荷叶饮': ['冬瓜', '荷叶'],
        '古法酸梅汤': ['乌梅', '话梅', '山楂'],
        '小吊梨汤': ['雪花梨'],
        '桑菊润燥茶': ['桑叶', '杭白菊'],
        '桂花酒酿饮': ['桂花', '酒酿'],
        '蜜桃乌龙冷萃': ['水蜜桃', '乌龙茶包'],
        '黄芪枸杞茶': ['黄芪', '枸杞'],
        '竹蔗茅根马蹄水': ['甘蔗', '白茅根', '马蹄']
    };

    return recipeIngredients[recipeName] || [];
}

// 开始加工
function startProcessing(recipeName) {
    const recipe = gameData.processingRecipes[recipeName];

    if (gameData.processingBoard.state !== 'idle') {
        addMessage('❌ 加工台正在使用中');
        return;
    }

    // 检查材料
    const hasIngredients = recipe.ingredients.every(ingredient =>
        gameData.inventory[ingredient] && gameData.inventory[ingredient] > 0
    );

    if (!hasIngredients) {
        addMessage('❌ 材料不足');
        return;
    }

    // 消耗材料
    recipe.ingredients.forEach(ingredient => {
        gameData.inventory[ingredient]--;
    });

    // 开始加工
    gameData.processingBoard.state = 'processing';
    gameData.processingBoard.recipe = recipeName;
    gameData.processingBoard.startTime = Date.now();
    gameData.processingBoard.duration = recipe.time;

    addMessage(`🔪 开始加工 ${recipeName}`);

    // 刷新显示
    updateProcessingBoard();
    initProcessingRecipes();
}
