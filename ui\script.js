// 🍵 可爱茶铺 - 完整游戏脚本
console.log('🍵 可爱茶铺启动中...');

// 游戏数据对象
const gameData = {
    // 季节和天气
    currentSeason: "春天",
    currentWeather: "晴天",
    currentDay: 1,
    seasons: ["春天", "夏天", "秋天", "冬天"],
    weathers: ["晴天", "刮风", "下雨", "下雪", "阴天"],
    weatherDuration: 20000, // 毫秒
    weatherStartTime: Date.now(),
    daysInSeason: 0,
    daysPerSeason: 10,

    // 种子和库存
    seeds: {},
    seedInfo: {}, // 存储种子的详细信息
    inventory: {},
    selectedSeedType: null,

    // 农田
    plots: [
        {
            id: 0,
            state: 'empty',
            growthStage: 0,
            stageStartTime: 0,
            moisture: 50,
            fertility: 50,
            plantType: null
        },
        {
            id: 1,
            state: 'empty',
            growthStage: 0,
            stageStartTime: 0,
            moisture: 50,
            fertility: 50,
            plantType: null
        },
        {
            id: 2,
            state: 'empty',
            growthStage: 0,
            stageStartTime: 0,
            moisture: 50,
            fertility: 50,
            plantType: null
        },
        {
            id: 3,
            state: 'empty',
            growthStage: 0,
            stageStartTime: 0,
            moisture: 50,
            fertility: 50,
            plantType: null
        }
    ],
    growthStages: ["长芽", "大叶子", "开花", "成熟"],
    stageDuration: 15000, // 毫秒，每个阶段15秒
    moistureConsumption: 10,
    fertilityConsumption: 5,
    minMoisture: 10,
    minFertility: 20,

    // 炉灶
    stoves: [
        {
            state: 'empty',
            startTime: 0,
            boilDuration: 20000, // 20秒
            recipe: null
        },
        {
            state: 'empty',
            startTime: 0,
            boilDuration: 20000, // 20秒
            recipe: null
        }
    ],

    // 茶饮
    madeTeas: [],
    teaTemps: {},
    teaMakeTimes: {},
    teaCoolingDuration: 20000,

    // 小料
    toppings: {
        "红糖": 5,
        "薄荷叶": 5,
        "姜丝": 5,
        "柚子丝": 5,
        "银耳丝": 5,
        "柠檬片": 5,
        "蜂蜜": 5,
        // 新增小料（初始为0，需要通过加工获得）
        "冰糖": 0,
        "乌龙茶包": 0,
        "干桂花": 0,
        "小圆子": 0,
        "酒酿": 0,
        "水蜜桃果肉": 0,
        "黄芪片": 0
    },

    // 顾客
    customer: {
        active: false,
        name: "暂无顾客",
        isVIP: false,
        teaChoice: null,
        toppingChoices: [],
        arrivalTime: 0,
        patience: 120000, // 普通顾客120秒
        maxPatience: 120000,
        served: false
    },
    customerSpawnCooldown: 5000, // 5秒检查一次是否生成新顾客
    lastCustomerTime: 0,
    customerNames: ['池惊暮', '凌小路', '江飞飞', '江三', '江四', '池云旗', '江潮', '江敕封', '花花', '姬别情', '池九信', '狸怒'],

    // 集卡系统
    collectedCards: {},

    // 消息
    messages: ["欢迎来到可爱茶铺!"],

    // 小篮子选择相关
    selectedPlotForPlanting: null,
    selectedSeedForPlanting: null,

    // 购物车
    cart: [],

    // 货币
    coins: 100,

    // 配方解锁系统
    unlockedRecipes: ["五味子饮", "柠檬茶"],
    customerVisits: {},
    servedCustomers: 0,

    // 配方解锁规则（按照规则MD更新）
    recipeUnlockRules: {
        "洛神玫瑰饮": { customer: "凌小路", visitsRequired: 1, chance: 1.0, guaranteedOnVisit: 1 },
        "桂圆红枣茶": { customer: "花花", visitsRequired: 1, chance: 1.0, guaranteedOnVisit: 1 },
        "焦香大麦茶": { customer: "江飞飞", visitsRequired: 2, chance: 1.0, guaranteedOnVisit: 2 },
        "三花决明茶": { customer: "江三", visitsRequired: 2, chance: 1.0, guaranteedOnVisit: 2 },
        "薄荷甘草凉茶": { customer: "江四", visitsRequired: 2, chance: 1.0, guaranteedOnVisit: 2 },
        "陈皮姜米茶": { customer: "池云旗", visitsRequired: 2, chance: 0.5, guaranteedOnVisit: 3 },
        "冬瓜荷叶饮": { customer: "江潮", visitsRequired: 3, chance: 0.6, guaranteedOnVisit: 4 },
        "古法酸梅汤": { customer: "池惊暮", visitsRequired: 2, chance: 0.3, guaranteedOnVisit: 3 },
        "小吊梨汤": { customer: "江敕封", visitsRequired: 3, chance: 0.4, guaranteedOnVisit: 5 },
    },

    // 加工配方
    processingRecipes: {
        '红糖': { ingredients: ['甘蔗'], time: 10000, output: 3 },
        '薄荷叶': { ingredients: ['薄荷'], time: 10000, output: 3 },
        '姜丝': { ingredients: ['生姜'], time: 10000, output: 3 },
        '柚子丝': { ingredients: ['柚子'], time: 10000, output: 3 },
        '银耳丝': { ingredients: ['银耳'], time: 15000, output: 3 },
        '柠檬片': { ingredients: ['柠檬'], time: 10000, output: 3 },
        // 新增加工配方
        '水蜜桃果肉': { ingredients: ['水蜜桃'], time: 12000, output: 3 },
        '黄芪片': { ingredients: ['黄芪'], time: 12000, output: 3 },
        '干桂花': { ingredients: ['桂花'], time: 10000, output: 3 },
        '小圆子': { ingredients: ['糯米'], time: 15000, output: 3 },
        '酒酿': { ingredients: ['米'], time: 18000, output: 3 }
    },

    // 加工台
    processingBoard: {
        state: 'idle', // idle, processing
        recipe: null,
        startTime: 0,
        duration: 0
    },

    // 商店物品
    shopItems: {
        '蜂蜜': { price: 3 },
        '银耳': { price: 3 },
        '红糖': { price: 2 },
        '薄荷叶': { price: 2 },
        // 新增物品
        '冰糖': { price: 3 },
        '乌龙茶包': { price: 4 }
    },

    // 人数解锁要求
    recipeUnlockRequirements: {
        "桑菊润燥茶": 30,
        "桂花酒酿饮": 60,
        "蜜桃乌龙冷萃": 90,
        "黄芪枸杞茶": 120,
        "竹蔗茅根马蹄水": 150
    },

    // 配方解锁故事（按照规则MD添加）
    recipeStories: {
        "洛神玫瑰饮": {
            title: "朱砂",
            content: "凌小路袖中藏着一盏温热的洛神玫瑰饮。'疏肝解郁的，好好学学，飞飞来了就做给他。跟他说就说养颜的茶方子'挑眉笑时，眼底却映着刀光，袍角还沾着血。",
            effect: "疏肝解郁，美白养颜，活血调经，适合女子日常饮用。"
        },
        "桂圆红枣茶": {
            title: "无归",
            content: "花花去凌雪坟前扫墓，手里拿着他最喜欢她给他做的茶。只是这一次只能自己做了。'自己给自己作茶怎么行，这方子给你们，以后我就来这里喝吧'",
            effect: "补血益气，安神养心，滋阴润燥，适合体弱或熬夜者饮用。"
        },
        "焦香大麦茶": {
            title: "雪夜",
            content: "长安冬夜，江飞飞蜷在凌雪阁的屋檐上，指尖冻得发僵。江三翻上屋顶，扔来一壶滚烫的大麦茶：'怂样，喝两口。'茶雾氤氲里，他忽然想起幼时第一次握刀，也是这焦苦的甜香压住了颤抖。",
            effect: "暖胃消食，缓解焦虑，安定心神，适合秋冬饮用。"
        },
        "三花决明茶": {
            title: "夜狩",
            content: "江四执刀归来，见江三伏案瞌睡，手边一盏凉透的三花决明茶。他轻叹，将外袍披上兄长肩头——却不知昨夜自己任务单上那三个名字，早已被江三的血刃划去。茶渣沉底，如未愈的旧伤。",
            effect: "清肝明目，清热解毒，缓解眼疲劳，适合长期伏案或夜视者饮用。"
        },
        "薄荷甘草凉茶": {
            title: "三哥",
            content: "江四给江三泡的茶，清清凉凉的，他那么爱出汗，肯定喜欢。茶叶刚放下，就听到三哥在院子里训练的刀声，他悄悄探头看了一眼，决定加多一片薄荷叶。",
            effect: "清热解暑，润喉止咳，提神醒脑，适合夏季饮用。"
        },
        "陈皮姜米茶": {
            title: "师徒",
            content: "池云旗心疼那小家伙，以前也不懂自己照顾自己，这茶是她专门给他找医师抄的方子。'别总吃那些乱七八糟的东西，胃疼了可别来找师父'虽然嘴上这么说，她还是悄悄在茶里多加了一片陈皮。",
            effect: "健脾和胃，理气化痰，温中散寒，适合消化不良或胃寒者饮用。"
        },
        "冬瓜荷叶饮": {
            title: "师徒2",
            content: "江潮给师父弄的消暑茶，荷叶是自己趴在池塘边采的，冬瓜也是自己种的。'师父，您尝尝，我按照您说的方法做的'他小心翼翼地端着茶，生怕师父不喜欢，却不知道池云旗早已欣慰地笑了。",
            effect: "清热利湿，消肿减脂，美容养颜，适合夏季消暑或减肥者饮用。"
        },
        "古法酸梅汤": {
            title: "梅香",
            content: "长安暑夜，池惊暮执剑伏于屋脊。目标出现时，她正饮尽最后一滴酸梅汤。瓷碗坠地碎响混着喉骨断裂声，梅妃教的小方子——杀人时唇齿间该留着甜味，才不苦。",
            effect: "生津止渴，消暑解腻，健脾开胃，缓解燥热，唐代已是宫廷消暑佳饮。"
        },
        "小吊梨汤": {
            title: "琴心",
            content: "江敕封抚琴时总爱在身边放一盏小吊梨汤，琴声悠扬，茶香袅袅。他说琴如人生，需要慢慢调教；茶如心境，需要细细品味。一曲终了，一盏茶尽，都是这世间最温柔的时光。",
            effect: "润肺止咳，清热降火，滋阴美容，宫廷传统滋补佳品。"
        }
    },

    // 当前活动标签
    activeTab: 'farm',
    // 当前信息滑块索引
    currentSlide: 0
};

// 初始化材料
const MATERIALS = [
    "五味子", "乌梅", "山楂", "陈皮", "甘草", "桂花", "大麦",
    "菊花", "金银花", "决明子", "枸杞", "生姜", "桂圆", "红枣",
    "薄荷", "玫瑰花", "洛神花", "冬瓜", "荷叶", "薏米", "雪花梨",
    "话梅", "甘蔗", "柚子", "柠檬",
    // 新增种子
    "桑叶", "杭白菊", "水蜜桃", "黄芪", "白茅根", "马蹄", "糯米", "米"
];

// 初始化种子和库存
MATERIALS.forEach(material => {
    // 将种子信息存储在单独的对象中
    gameData.seedInfo[material] = {
        price: 1,
        growTime: 30000,
        yield: material
    };
    // 种子数量初始化为0
    gameData.seeds[material] = 0;
    gameData.inventory[material] = 1; // 每种材料初始化为1个
});

// 设置特殊种子的价格和生长时间（按照规则MD）
const SPECIAL_SEED_CONFIG = {
    "桑叶": { price: 2, growTime: 45000 },      // 45秒
    "杭白菊": { price: 2, growTime: 50000 },    // 50秒
    "水蜜桃": { price: 3, growTime: 60000 },    // 60秒
    "黄芪": { price: 3, growTime: 55000 },      // 55秒
    "白茅根": { price: 2, growTime: 40000 },    // 40秒
    "马蹄": { price: 2, growTime: 45000 },      // 45秒
    "糯米": { price: 2, growTime: 50000 },      // 50秒
    "米": { price: 1, growTime: 40000 }         // 40秒
};

// 应用特殊种子配置
Object.keys(SPECIAL_SEED_CONFIG).forEach(seedName => {
    if (gameData.seedInfo[seedName]) {
        gameData.seedInfo[seedName].price = SPECIAL_SEED_CONFIG[seedName].price;
        gameData.seedInfo[seedName].growTime = SPECIAL_SEED_CONFIG[seedName].growTime;
    }
});

// 游戏状态
let isPaused = false;
let isTestMode = false;

// 调试函数
function debug(message) {
    console.log(`[可爱茶铺] ${message}`);
}

// 添加消息函数
function addMessage(message) {
    gameData.messages.push(message);
    if (gameData.messages.length > 10) {
        gameData.messages.shift(); // 保持最多10条消息
    }
    updateMessageDisplay();
}

// 更新消息显示
function updateMessageDisplay() {
    const messageText = document.getElementById('message-text');
    if (messageText && gameData.messages.length > 0) {
        messageText.textContent = gameData.messages[gameData.messages.length - 1];

        // 显示消息气泡
        const messageBubble = document.getElementById('message-bubble');
        if (messageBubble) {
            messageBubble.classList.remove('hidden');

            // 3秒后隐藏消息
            setTimeout(() => {
                messageBubble.classList.add('hidden');
            }, 3000);
        }
    }
}

// 页面加载完成后初始化游戏
document.addEventListener('DOMContentLoaded', function() {
    debug('DOM加载完成，开始初始化游戏...');
    initGame();
});

// 游戏初始化函数
function initGame() {
    debug('初始化游戏...');

    // 注册Service Worker
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('./service-worker.js')
            .then(registration => {
                debug('Service Worker 注册成功');
            })
            .catch(error => {
                debug('Service Worker 注册失败: ' + error);
            });
    }

    // 初始化界面
    initUI();

    // 初始化事件监听器
    initEventListeners();

    // 更新所有显示
    updateAllDisplays();

    // 启动游戏循环
    startGameLoop();

    // 显示欢迎消息
    addMessage('🍵 欢迎来到可爱茶铺！开始您的经营之旅吧～');

    debug('游戏初始化完成！');
}

// 初始化UI界面
function initUI() {
    debug('初始化UI界面...');

    // 初始化农场网格
    initFarmGrid();

    // 初始化厨房区域
    initKitchen();

    // 初始化茶摊区域
    initTeaShop();

    // 初始化小料网格
    initToppingsGrid();

    debug('UI界面初始化完成');
}

// 初始化农场网格
function initFarmGrid() {
    const farmGrid = document.getElementById('farm-grid');
    if (!farmGrid) return;

    farmGrid.innerHTML = '';

    gameData.plots.forEach((plot, index) => {
        const plotCard = createPlotCard(plot, index);
        farmGrid.appendChild(plotCard);
    });
}

// 创建田地卡片
function createPlotCard(plot, index) {
    const plotCard = document.createElement('div');
    plotCard.className = `plot-card ${plot.state === 'empty' ? 'empty' : ''}`;
    plotCard.dataset.plotId = index;

    plotCard.innerHTML = `
        <div class="plot-header">
            <span class="plot-number">${index + 1}</span>
            <button class="plot-action" data-plot="${index}">
                ${getPlotActionText(plot)}
            </button>
        </div>
        <div class="plot-visual">
            <div class="soil"></div>
            ${getPlotVisualContent(plot)}
        </div>
        <div class="plot-stats">
            <div class="stat">
                <span class="stat-icon">💧</span>
                <span class="stat-value">${plot.moisture}%</span>
            </div>
            <div class="stat">
                <span class="stat-icon">🌿</span>
                <span class="stat-value">${plot.fertility}%</span>
            </div>
        </div>
        ${getPlotTimerContent(plot)}
    `;

    return plotCard;
}

// 获取田地操作按钮文本
function getPlotActionText(plot) {
    switch (plot.state) {
        case 'empty':
            return '🌰 种植';
        case 'growing':
            return '🌱 生长中';
        case 'mature':
            return '🎉 收获';
        default:
            return '➕ 种植';
    }
}

// 获取田地视觉内容
function getPlotVisualContent(plot) {
    if (plot.state === 'empty') {
        return '<div class="empty-hint">点击种植</div>';
    } else if (plot.state === 'growing') {
        const stageEmojis = ['🌱', '🌿', '🌸', '🌺'];
        const emoji = stageEmojis[plot.growthStage] || '🌱';
        return `<div class="plant growing">${emoji}</div>`;
    } else if (plot.state === 'mature') {
        return '<div class="plant mature">🌺</div>';
    }
    return '';
}

// 获取田地计时器内容
function getPlotTimerContent(plot) {
    if (plot.state === 'growing') {
        return '<div class="plot-timer">⏰ 生长中...</div>';
    } else if (plot.state === 'mature') {
        return '<div class="plot-ready">✨ 可以收获了！</div>';
    }
    return '<div class="plot-timer"></div>';
}

// 初始化厨房区域
function initKitchen() {
    const stovesGrid = document.getElementById('stoves-grid');
    if (!stovesGrid) return;

    stovesGrid.innerHTML = '';

    gameData.stoves.forEach((stove, index) => {
        const stoveCard = createStoveCard(stove, index);
        stovesGrid.appendChild(stoveCard);
    });

    // 初始化加工台
    initProcessingBoard();
}

// 创建炉灶卡片
function createStoveCard(stove, index) {
    const stoveCard = document.createElement('div');
    stoveCard.className = `stove-card ${stove.state === 'empty' ? 'empty' : stove.state}`;
    stoveCard.dataset.stoveId = index;

    if (stove.state === 'empty') {
        stoveCard.innerHTML = `
            <div class="stove-visual">
                <div class="pot empty">🫖</div>
            </div>
            <div class="stove-info">
                <div class="empty-text">点击制茶</div>
            </div>
            <button class="start-cooking" data-stove="${index}">开始制茶</button>
        `;
    } else if (stove.state === 'cooking') {
        const progress = getStoveProgress(stove);
        stoveCard.innerHTML = `
            <div class="stove-visual">
                <div class="fire">🔥</div>
                <div class="pot">🫖</div>
                <div class="steam">💨</div>
            </div>
            <div class="stove-info">
                <div class="recipe-name">${stove.recipe || '制茶中'}</div>
                <div class="cooking-timer">⏰ 制作中...</div>
            </div>
            <div class="cooking-progress">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${progress}%"></div>
                </div>
            </div>
        `;
    }

    return stoveCard;
}

// 获取炉灶进度
function getStoveProgress(stove) {
    if (stove.state !== 'cooking') return 0;

    const elapsed = Date.now() - stove.startTime;
    const progress = Math.min((elapsed / stove.boilDuration) * 100, 100);
    return Math.round(progress);
}

// 初始化加工台
function initProcessingBoard() {
    const processingBoard = document.getElementById('processing-board');
    if (!processingBoard) return;

    updateProcessingBoard();
    initProcessingRecipes();
}

// 更新加工台显示
function updateProcessingBoard() {
    const processingInfo = document.getElementById('processing-info');
    const processingIngredients = document.getElementById('processing-ingredients');

    if (!processingInfo || !processingIngredients) return;

    if (gameData.processingBoard.state === 'idle') {
        processingInfo.innerHTML = '<div class="processing-status">点击选择加工配方</div>';
        processingIngredients.textContent = '🥕🌿';
    } else if (gameData.processingBoard.state === 'processing') {
        const elapsed = Date.now() - gameData.processingBoard.startTime;
        const remaining = Math.max(0, gameData.processingBoard.duration - elapsed);
        const seconds = Math.ceil(remaining / 1000);

        processingInfo.innerHTML = `
            <div class="processing-status">正在加工 ${gameData.processingBoard.recipe}</div>
            <div class="processing-timer">⏰ ${seconds}秒</div>
        `;
        processingIngredients.textContent = '⚙️🔄';
    }
}

// 初始化加工配方
function initProcessingRecipes() {
    const processingRecipes = document.getElementById('processing-recipes');
    if (!processingRecipes) return;

    processingRecipes.innerHTML = '';

    Object.keys(gameData.processingRecipes).forEach(recipeName => {
        const recipe = gameData.processingRecipes[recipeName];
        const button = document.createElement('button');
        button.className = 'recipe-chip';
        button.textContent = `${recipe.ingredients[0]}→${recipeName}`;
        button.dataset.recipe = recipeName;

        // 检查是否有足够的材料
        const hasIngredients = recipe.ingredients.every(ingredient =>
            gameData.inventory[ingredient] && gameData.inventory[ingredient] > 0
        );

        if (!hasIngredients) {
            button.classList.add('disabled');
        }

        processingRecipes.appendChild(button);
    });
}

// 初始化茶摊区域
function initTeaShop() {
    updateTeaDisplay();
}

// 更新茶饮显示
function updateTeaDisplay() {
    const teaDisplay = document.getElementById('tea-display');
    if (!teaDisplay) return;

    teaDisplay.innerHTML = '';

    if (gameData.madeTeas.length === 0) {
        teaDisplay.innerHTML = `
            <div class="no-tea-hint">
                <span class="hint-icon">🫖</span>
                <span class="hint-text">还没有制作好的茶饮哦～</span>
            </div>
        `;
        return;
    }

    gameData.madeTeas.forEach((tea, index) => {
        const teaItem = createTeaItem(tea, index);
        teaDisplay.appendChild(teaItem);
    });
}

// 创建茶饮项目
function createTeaItem(tea, index) {
    const teaItem = document.createElement('div');
    teaItem.className = 'tea-item';
    teaItem.dataset.teaId = tea.id;

    const isHot = gameData.teaTemps[tea.id] === 'hot';
    const tempClass = isHot ? 'hot' : 'cold';
    const tempIcon = isHot ? '🔥' : '❄️';
    const tempText = isHot ? '热茶' : '冰茶';
    const effectIcon = isHot ? '✨' : '❄️';

    teaItem.innerHTML = `
        <div class="tea-visual">
            <div class="tea-cup">${isHot ? '🍵' : '🧊'}</div>
            <div class="${isHot ? 'steam-effect' : 'ice-effect'}">${effectIcon}</div>
        </div>
        <div class="tea-info">
            <div class="tea-name">${tea.name}</div>
            <div class="tea-temp ${tempClass}">${tempText} ${tempIcon}</div>
        </div>
        <div class="tea-actions">
            <button class="serve-button" data-tea-index="${index}">🎉 服务顾客</button>
            <button class="add-topping" data-tea-index="${index}">➕ 加料</button>
        </div>
    `;

    return teaItem;
}

// 初始化小料网格
function initToppingsGrid() {
    updateToppingsDisplay();
}

// 更新小料显示
function updateToppingsDisplay() {
    const toppingsGrid = document.getElementById('toppings-grid');
    if (!toppingsGrid) return;

    toppingsGrid.innerHTML = '';

    Object.entries(gameData.toppings).forEach(([toppingName, count]) => {
        if (count > 0) { // 只显示有库存的小料
            const toppingItem = createToppingItem(toppingName, count);
            toppingsGrid.appendChild(toppingItem);
        }
    });
}

// 创建小料项目
function createToppingItem(toppingName, count) {
    const toppingItem = document.createElement('div');
    toppingItem.className = 'topping-item';
    toppingItem.dataset.topping = toppingName;

    const toppingIcon = getToppingIcon(toppingName);

    toppingItem.innerHTML = `
        <span class="topping-icon">${toppingIcon}</span>
        <span class="topping-name">${toppingName}</span>
        <span class="topping-count">x${count}</span>
    `;

    return toppingItem;
}

// 获取小料图标
function getToppingIcon(toppingName) {
    const icons = {
        '红糖': '🟤',
        '薄荷叶': '🌿',
        '姜丝': '🫚',
        '柚子丝': '🍊',
        '银耳丝': '🤍',
        '柠檬片': '🍋',
        '蜂蜜': '🍯',
        '冰糖': '🧊',
        '乌龙茶包': '🍃',
        '干桂花': '🌼',
        '小圆子': '⚪',
        '酒酿': '🍶',
        '水蜜桃果肉': '🍑',
        '黄芪片': '🟡'
    };
    return icons[toppingName] || '🌿';
}

// 初始化事件监听器
function initEventListeners() {
    debug('初始化事件监听器...');

    // 选项卡切换
    initTabSwitching();

    // 菜单按钮
    initMenuButton();

    // 农场相关事件
    initFarmEvents();

    // 厨房相关事件
    initKitchenEvents();

    // 茶摊相关事件
    initTeaShopEvents();

    // 购物车按钮
    initCartButton();

    debug('事件监听器初始化完成');
}

// 初始化选项卡切换
function initTabSwitching() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.dataset.tab;

            // 移除所有活动状态
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // 添加活动状态
            button.classList.add('active');
            const targetContent = document.getElementById(targetTab);
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // 更新游戏数据
            gameData.activeTab = targetTab;

            // 刷新当前选项卡的显示
            refreshCurrentTab(targetTab);
        });
    });
}

// 刷新当前选项卡
function refreshCurrentTab(tabName) {
    switch (tabName) {
        case 'farm':
            initFarmGrid();
            break;
        case 'kitchen':
            initKitchen();
            break;
        case 'shop':
            updateTeaDisplay();
            updateToppingsDisplay();
            break;
    }
}

// 初始化菜单按钮
function initMenuButton() {
    const menuButton = document.getElementById('menu-button');
    const menuPanel = document.getElementById('menu-panel');
    const closeMenu = document.getElementById('close-menu');

    if (menuButton && menuPanel) {
        menuButton.addEventListener('click', () => {
            menuPanel.style.display = 'block';
        });
    }

    if (closeMenu && menuPanel) {
        closeMenu.addEventListener('click', () => {
            menuPanel.style.display = 'none';
        });
    }

    // 菜单项事件
    initMenuItems();
}

// 初始化菜单项
function initMenuItems() {
    const saveGameBtn = document.getElementById('save-game-btn');
    const loadGameBtn = document.getElementById('load-game-btn');
    const recipeBookBtn = document.getElementById('recipe-book-btn');
    const testModeBtn = document.getElementById('test-mode-btn');
    const testWindowBtn = document.getElementById('test-window-btn');

    if (saveGameBtn) {
        saveGameBtn.addEventListener('click', () => {
            saveGame();
            addMessage('💾 游戏已保存');
            hideMenu();
        });
    }

    if (loadGameBtn) {
        loadGameBtn.addEventListener('click', () => {
            loadGame();
            addMessage('📁 游戏已加载');
            hideMenu();
        });
    }

    if (recipeBookBtn) {
        recipeBookBtn.addEventListener('click', () => {
            showRecipeBook();
            hideMenu();
        });
    }

    if (testModeBtn) {
        testModeBtn.addEventListener('click', () => {
            showTestPanel();
            hideMenu();
        });
    }

    if (testWindowBtn) {
        testWindowBtn.addEventListener('click', () => {
            openTestWindow();
            hideMenu();
        });
    }
}

// 隐藏菜单
function hideMenu() {
    const menuPanel = document.getElementById('menu-panel');
    if (menuPanel) {
        menuPanel.style.display = 'none';
    }
}

// 初始化农场事件
function initFarmEvents() {
    // 田地点击事件
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('plot-action')) {
            const plotIndex = parseInt(e.target.dataset.plot);
            handlePlotAction(plotIndex);
        }
    });

    // 快捷操作按钮
    const waterAllBtn = document.getElementById('water-all-btn');
    const fertilizeAllBtn = document.getElementById('fertilize-all-btn');
    const basketBtn = document.getElementById('basket-btn');

    if (waterAllBtn) {
        waterAllBtn.addEventListener('click', () => {
            waterAllPlots();
        });
    }

    if (fertilizeAllBtn) {
        fertilizeAllBtn.addEventListener('click', () => {
            fertilizeAllPlots();
        });
    }

    if (basketBtn) {
        basketBtn.addEventListener('click', () => {
            showBasketPanel();
        });
    }
}

// 处理田地操作
function handlePlotAction(plotIndex) {
    const plot = gameData.plots[plotIndex];

    if (plot.state === 'empty') {
        // 种植操作
        showBasketPanel(plotIndex);
    } else if (plot.state === 'mature') {
        // 收获操作
        harvestPlot(plotIndex);
    }
}

// 收获田地
function harvestPlot(plotIndex) {
    const plot = gameData.plots[plotIndex];

    if (plot.state !== 'mature') return;

    const harvestedItem = plot.plantType;
    const harvestAmount = Math.floor(Math.random() * 3) + 2; // 2-4个

    // 添加到库存
    if (!gameData.inventory[harvestedItem]) {
        gameData.inventory[harvestedItem] = 0;
    }
    gameData.inventory[harvestedItem] += harvestAmount;

    // 重置田地
    plot.state = 'empty';
    plot.growthStage = 0;
    plot.stageStartTime = 0;
    plot.plantType = null;
    plot.moisture = Math.max(plot.moisture - 20, 0);
    plot.fertility = Math.max(plot.fertility - 15, 0);

    addMessage(`🎉 收获了 ${harvestAmount} 个 ${harvestedItem}`);

    // 刷新显示
    initFarmGrid();
}

// 浇水所有田地
function waterAllPlots() {
    let wateredCount = 0;

    gameData.plots.forEach(plot => {
        if (plot.moisture < 100) {
            plot.moisture = Math.min(plot.moisture + 30, 100);
            wateredCount++;
        }
    });

    if (wateredCount > 0) {
        addMessage(`💧 为 ${wateredCount} 块田地浇了水`);
        initFarmGrid();
    } else {
        addMessage('💧 所有田地的湿度都很充足');
    }
}

// 施肥所有田地
function fertilizeAllPlots() {
    let fertilizedCount = 0;

    gameData.plots.forEach(plot => {
        if (plot.fertility < 100) {
            plot.fertility = Math.min(plot.fertility + 25, 100);
            fertilizedCount++;
        }
    });

    if (fertilizedCount > 0) {
        addMessage(`🌿 为 ${fertilizedCount} 块田地施了肥`);
        initFarmGrid();
    } else {
        addMessage('🌿 所有田地的肥力都很充足');
    }
}

// 初始化厨房事件
function initKitchenEvents() {
    // 炉灶点击事件
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('start-cooking')) {
            const stoveIndex = parseInt(e.target.dataset.stove);
            startCooking(stoveIndex);
        }
    });

    // 加工配方点击事件
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('recipe-chip') && !e.target.classList.contains('disabled')) {
            const recipeName = e.target.dataset.recipe;
            startProcessing(recipeName);
        }
    });
}

// 开始制茶
function startCooking(stoveIndex) {
    const stove = gameData.stoves[stoveIndex];

    if (stove.state !== 'empty') return;

    // 显示配方选择面板
    showRecipeSelectionPanel(stoveIndex);
}

// 显示配方选择面板
function showRecipeSelectionPanel(stoveIndex) {
    const panel = document.createElement('div');
    panel.className = 'recipe-selection-panel';
    panel.style.cssText = `
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        background: white;
        border-radius: 20px;
        padding: 20px;
        box-shadow: 0 8px 25px rgba(76, 175, 80, 0.25);
        z-index: 1001;
        max-width: 90vw;
        max-height: 80vh;
        overflow-y: auto;
        min-width: 350px;
    `;

    // 获取所有已解锁的配方（不管材料是否足够）
    const unlockedRecipes = gameData.unlockedRecipes;

    panel.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h3 style="margin: 0; color: #2E7D32;">🍵 选择制茶配方</h3>
            <button class="close-recipe-selection" style="background: none; border: none; font-size: 24px; cursor: pointer;">×</button>
        </div>
        <div style="margin-bottom: 15px; color: #666; font-size: 14px;">
            为炉灶${stoveIndex + 1}选择要制作的茶饮：
        </div>
        <div class="recipe-options" style="display: grid; gap: 10px;">
            ${unlockedRecipes.length > 0 ? unlockedRecipes.map(recipe => {
                const ingredients = getRecipeIngredients(recipe);
                const canMake = canMakeRecipe(recipe);
                const missingIngredients = getMissingIngredients(recipe);

                return `
                    <button class="recipe-option ${canMake ? '' : 'disabled'}" data-recipe="${recipe}" data-stove="${stoveIndex}" style="
                        background: ${canMake ? '#E8F5E8' : '#FFF5F5'};
                        border: 2px solid ${canMake ? '#4CAF50' : '#FF9800'};
                        border-radius: 12px;
                        padding: 15px;
                        cursor: ${canMake ? 'pointer' : 'not-allowed'};
                        transition: all 0.3s ease;
                        text-align: left;
                        opacity: ${canMake ? '1' : '0.8'};
                    " ${canMake ? '' : 'disabled'}>
                        <div style="font-weight: bold; color: ${canMake ? '#2E7D32' : '#E65100'}; margin-bottom: 8px;">
                            ${canMake ? '✅' : '❌'} ${recipe}
                        </div>
                        <div style="font-size: 12px; color: #666; margin-bottom: 6px;">
                            所需材料：${ingredients.join('、')}
                        </div>
                        ${!canMake ? `
                            <div style="font-size: 11px; color: #D32F2F; background: #FFEBEE; padding: 4px 8px; border-radius: 6px;">
                                缺少：${missingIngredients.join('、')}
                            </div>
                        ` : `
                            <div style="font-size: 11px; color: #4CAF50;">
                                ✅ 材料充足
                            </div>
                        `}
                    </button>
                `;
            }).join('') : '<div style="text-align: center; color: #999; padding: 20px;">暂无已解锁的配方</div>'}
        </div>
    `;

    document.body.appendChild(panel);

    // 添加事件监听器
    panel.querySelector('.close-recipe-selection').addEventListener('click', () => {
        document.body.removeChild(panel);
    });

    panel.querySelectorAll('.recipe-option:not(.disabled)').forEach(btn => {
        btn.addEventListener('click', () => {
            const recipeName = btn.dataset.recipe;
            const stoveIndex = parseInt(btn.dataset.stove);

            // 开始制作选中的配方
            startCookingRecipe(stoveIndex, recipeName);

            document.body.removeChild(panel);
        });

        btn.addEventListener('mouseenter', () => {
            if (!btn.classList.contains('disabled')) {
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            }
        });

        btn.addEventListener('mouseleave', () => {
            if (!btn.classList.contains('disabled')) {
                btn.style.background = '#E8F5E8';
                btn.style.color = '#2E7D32';
            }
        });
    });
}

// 开始制作指定配方
function startCookingRecipe(stoveIndex, recipeName) {
    const stove = gameData.stoves[stoveIndex];

    if (stove.state !== 'empty') {
        addMessage('❌ 炉灶正在使用中');
        return;
    }

    if (!canMakeRecipe(recipeName)) {
        addMessage('❌ 材料不足，无法制作');
        return;
    }

    // 消耗材料
    const recipeIngredients = getRecipeIngredients(recipeName);
    recipeIngredients.forEach(ingredient => {
        gameData.inventory[ingredient]--;
    });

    // 开始制茶
    stove.state = 'cooking';
    stove.recipe = recipeName;
    stove.startTime = Date.now();

    addMessage(`🔥 开始制作 ${recipeName}`);

    // 刷新显示
    initKitchen();
}

// 检查是否可以制作配方
function canMakeRecipe(recipeName) {
    const ingredients = getRecipeIngredients(recipeName);
    return ingredients.every(ingredient =>
        gameData.inventory[ingredient] && gameData.inventory[ingredient] > 0
    );
}

// 获取缺少的材料
function getMissingIngredients(recipeName) {
    const ingredients = getRecipeIngredients(recipeName);
    const missing = [];

    for (const ingredient of ingredients) {
        if (!gameData.inventory[ingredient] || gameData.inventory[ingredient] <= 0) {
            missing.push(ingredient);
        }
    }

    return missing;
}

// 获取配方所需材料（按照规则MD更新）
function getRecipeIngredients(recipeName) {
    const recipeIngredients = {
        // 基础配方（简单配方）
        '五味子饮': ['五味子'],
        '柠檬茶': ['柠檬'],

        // 特殊顾客解锁配方（按规则MD修正）
        '洛神玫瑰饮': ['洛神花', '玫瑰花', '山楂'],
        '桂圆红枣茶': ['桂圆', '红枣', '枸杞'],
        '焦香大麦茶': ['大麦'],
        '三花决明茶': ['菊花', '金银花', '决明子', '枸杞'],
        '薄荷甘草凉茶': ['薄荷', '甘草'],
        '陈皮姜米茶': ['陈皮', '生姜'],
        '冬瓜荷叶饮': ['冬瓜', '荷叶', '薏米'],
        '古法酸梅汤': ['乌梅', '山楂', '陈皮', '甘草', '桂花'],
        '小吊梨汤': ['雪花梨', '银耳', '话梅', '枸杞'],

        // 人数解锁配方
        '桑菊润燥茶': ['桑叶', '杭白菊'],
        '桂花酒酿饮': ['桂花', '酒酿'],
        '蜜桃乌龙冷萃': ['水蜜桃', '乌龙茶包'],
        '黄芪枸杞茶': ['黄芪', '枸杞'],
        '竹蔗茅根马蹄水': ['甘蔗', '白茅根', '马蹄']
    };

    return recipeIngredients[recipeName] || [];
}

// 开始加工
function startProcessing(recipeName) {
    const recipe = gameData.processingRecipes[recipeName];

    if (gameData.processingBoard.state !== 'idle') {
        addMessage('❌ 加工台正在使用中');
        return;
    }

    // 检查材料
    const hasIngredients = recipe.ingredients.every(ingredient =>
        gameData.inventory[ingredient] && gameData.inventory[ingredient] > 0
    );

    if (!hasIngredients) {
        addMessage('❌ 材料不足');
        return;
    }

    // 消耗材料
    recipe.ingredients.forEach(ingredient => {
        gameData.inventory[ingredient]--;
    });

    // 开始加工
    gameData.processingBoard.state = 'processing';
    gameData.processingBoard.recipe = recipeName;
    gameData.processingBoard.startTime = Date.now();
    gameData.processingBoard.duration = recipe.time;

    addMessage(`🔪 开始加工 ${recipeName}`);

    // 刷新显示
    updateProcessingBoard();
    initProcessingRecipes();
}

// 初始化茶摊事件
function initTeaShopEvents() {
    // 服务顾客按钮
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('serve-button')) {
            const teaIndex = parseInt(e.target.dataset.teaIndex);
            serveCustomer(teaIndex);
        }
    });

    // 添加小料按钮
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('add-topping')) {
            const teaIndex = parseInt(e.target.dataset.teaIndex);
            showToppingSelection(teaIndex);
        }
    });
}

// 服务顾客
function serveCustomer(teaIndex) {
    if (!gameData.customer.active) {
        addMessage('❌ 没有顾客在等待');
        return;
    }

    const tea = gameData.madeTeas[teaIndex];
    if (!tea) {
        addMessage('❌ 茶饮不存在');
        return;
    }

    // 检查茶饮是否匹配顾客需求
    if (tea.name !== gameData.customer.teaChoice) {
        addMessage(`❌ 顾客要的是 ${gameData.customer.teaChoice}，不是 ${tea.name}`);
        return;
    }

    // 检查小料是否匹配
    const customerToppings = gameData.customer.toppingChoices || [];
    const teaToppings = tea.toppings || [];

    const missingToppings = customerToppings.filter(topping => !teaToppings.includes(topping));
    if (missingToppings.length > 0) {
        addMessage(`❌ 还需要添加小料：${missingToppings.join('、')}`);
        return;
    }

    // 计算收入
    const basePrice = 10;
    const toppingBonus = teaToppings.length * 2;
    const vipBonus = gameData.customer.isVIP ? 5 : 0;
    const temperatureBonus = gameData.teaTemps[tea.id] === 'hot' ? 3 : 0;

    const totalPrice = basePrice + toppingBonus + vipBonus + temperatureBonus;

    // 获得收入
    gameData.coins += totalPrice;

    // 移除茶饮
    gameData.madeTeas.splice(teaIndex, 1);
    delete gameData.teaTemps[tea.id];
    delete gameData.teaMakeTimes[tea.id];

    // 记录服务顾客
    gameData.servedCustomers = (gameData.servedCustomers || 0) + 1;

    // 记录特殊顾客访问
    if (gameData.customer.isVIP && gameData.customerNames.includes(gameData.customer.name)) {
        if (!gameData.customerVisits) {
            gameData.customerVisits = {};
        }
        gameData.customerVisits[gameData.customer.name] =
            (gameData.customerVisits[gameData.customer.name] || 0) + 1;

        // 检查配方解锁
        checkRecipeUnlock(gameData.customer.name);
    }

    // 显示成功消息
    const customerName = gameData.customer.name;
    addMessage(`🎉 成功服务 ${customerName}，获得 ${totalPrice} 铜板！`);

    // 重置顾客
    resetCustomer();

    // 更新显示
    updateAllDisplays();
}

// 显示小料选择
function showToppingSelection(teaIndex) {
    const tea = gameData.madeTeas[teaIndex];
    if (!tea) return;

    // 创建小料选择面板
    const panel = document.createElement('div');
    panel.className = 'topping-selection-panel';
    panel.style.cssText = `
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        background: white;
        border-radius: 20px;
        padding: 20px;
        box-shadow: 0 8px 25px rgba(76, 175, 80, 0.25);
        z-index: 1001;
        max-width: 90vw;
        max-height: 80vh;
        overflow-y: auto;
    `;

    panel.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h3 style="margin: 0; color: #2E7D32;">为 ${tea.name} 添加小料</h3>
            <button class="close-topping-panel" style="background: none; border: none; font-size: 24px; cursor: pointer;">×</button>
        </div>
        <div class="available-toppings" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 10px;">
            ${Object.entries(gameData.toppings)
                .filter(([name, count]) => count > 0)
                .map(([name, count]) => `
                    <button class="topping-option" data-topping="${name}" style="
                        background: #E8F5E8;
                        border: 2px solid #81C784;
                        border-radius: 12px;
                        padding: 10px;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        text-align: center;
                    ">
                        <div style="font-size: 20px; margin-bottom: 4px;">${getToppingIcon(name)}</div>
                        <div style="font-size: 12px; font-weight: 500;">${name}</div>
                        <div style="font-size: 10px; color: #666;">x${count}</div>
                    </button>
                `).join('')}
        </div>
        <div style="margin-top: 15px; text-align: center;">
            <button class="confirm-toppings" style="
                background: #4CAF50;
                color: white;
                border: none;
                border-radius: 12px;
                padding: 10px 20px;
                font-size: 14px;
                cursor: pointer;
            ">确认添加</button>
        </div>
    `;

    document.body.appendChild(panel);

    // 添加事件监听器
    const selectedToppings = new Set(tea.toppings || []);

    panel.querySelectorAll('.topping-option').forEach(btn => {
        const toppingName = btn.dataset.topping;

        // 如果已经添加过，显示选中状态
        if (selectedToppings.has(toppingName)) {
            btn.style.background = '#4CAF50';
            btn.style.color = 'white';
        }

        btn.addEventListener('click', () => {
            if (selectedToppings.has(toppingName)) {
                // 移除小料
                selectedToppings.delete(toppingName);
                btn.style.background = '#E8F5E8';
                btn.style.color = '#2E7D32';
            } else {
                // 添加小料
                selectedToppings.add(toppingName);
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            }
        });
    });

    panel.querySelector('.close-topping-panel').addEventListener('click', () => {
        document.body.removeChild(panel);
    });

    panel.querySelector('.confirm-toppings').addEventListener('click', () => {
        // 更新茶饮的小料
        tea.toppings = Array.from(selectedToppings);

        // 消耗小料库存
        selectedToppings.forEach(toppingName => {
            if (gameData.toppings[toppingName] > 0) {
                gameData.toppings[toppingName]--;
            }
        });

        addMessage(`✅ 为 ${tea.name} 添加了小料：${Array.from(selectedToppings).join('、')}`);

        // 更新显示
        updateTeaDisplay();
        updateToppingsDisplay();

        // 关闭面板
        document.body.removeChild(panel);
    });
}

// 初始化购物车按钮
function initCartButton() {
    const cartButton = document.getElementById('cart-button');
    const shopBtn = document.getElementById('shop-btn');
    const inventoryBtn = document.getElementById('inventory-btn');

    if (cartButton) {
        cartButton.addEventListener('click', () => {
            showShopPanel();
        });
    }

    if (shopBtn) {
        shopBtn.addEventListener('click', () => {
            showShopPanel();
        });
    }

    if (inventoryBtn) {
        inventoryBtn.addEventListener('click', () => {
            showInventoryPanel();
        });
    }

    // 更新购物车徽章
    updateCartBadge();
}

// 更新购物车徽章
function updateCartBadge() {
    const cartBadge = document.getElementById('cart-badge');
    if (cartBadge) {
        const itemCount = gameData.cart.reduce((total, item) => total + item.quantity, 0);
        cartBadge.textContent = itemCount;
        cartBadge.style.display = itemCount > 0 ? 'flex' : 'none';
    }
}

// 重置顾客
function resetCustomer() {
    gameData.customer = {
        active: false,
        name: "暂无顾客",
        isVIP: false,
        teaChoice: null,
        toppingChoices: [],
        arrivalTime: 0,
        patience: 120000,
        maxPatience: 120000,
        served: false
    };

    updateCustomerDisplay();
}

// 检查配方解锁
function checkRecipeUnlock(customerName) {
    if (!gameData.customerVisits || !gameData.recipeUnlockRules) return;

    const visitCount = gameData.customerVisits[customerName] || 0;

    // 遍历解锁规则
    Object.entries(gameData.recipeUnlockRules).forEach(([recipe, rule]) => {
        // 只检查当前顾客对应的配方
        if (rule.customer === customerName) {
            // 如果配方已解锁，跳过
            if (gameData.unlockedRecipes.includes(recipe)) {
                return;
            }

            // 检查访问次数条件
            if (visitCount >= rule.visitsRequired) {
                let shouldUnlock = false;

                // 检查是否达到必定解锁的次数
                if (visitCount >= rule.guaranteedOnVisit) {
                    shouldUnlock = true;
                } else if (Math.random() < rule.chance) {
                    // 根据概率判断
                    shouldUnlock = true;
                }

                if (shouldUnlock) {
                    gameData.unlockedRecipes.push(recipe);
                    addMessage(`🔓 解锁新配方：${recipe}！`);
                    showRecipeUnlockStory(recipe);
                }
            }
        }
    });
}

// 显示配方解锁故事（更新为完整故事内容）
function showRecipeUnlockStory(recipeName) {
    const story = gameData.recipeStories[recipeName];

    // 创建故事弹窗
    const storyPanel = document.createElement('div');
    storyPanel.className = 'recipe-story-panel';
    storyPanel.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 1002;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: fadeInUp 0.5s ease;
    `;

    const storyContent = document.createElement('div');
    storyContent.style.cssText = `
        background: linear-gradient(135deg, #fff8e1, #f3e5ab);
        border-radius: 20px;
        padding: 30px;
        max-width: 90vw;
        max-height: 80vh;
        overflow-y: auto;
        text-align: left;
        box-shadow: 0 8px 25px rgba(139, 69, 19, 0.3);
        border: 3px solid #8b4513;
        font-family: 'KaiTi', '楷体', serif;
    `;

    if (story) {
        // 有故事内容的配方
        storyContent.innerHTML = `
            <div style="text-align: center; margin-bottom: 25px;">
                <div style="font-size: 48px; margin-bottom: 15px;">🍵</div>
                <h2 style="color: #8b4513; margin-bottom: 10px; font-size: 24px;">新配方解锁</h2>
                <h3 style="color: #2E7D32; margin-bottom: 5px; font-size: 20px;">${recipeName}</h3>
                <h4 style="color: #666; margin-bottom: 20px; font-size: 16px; font-style: italic;">${story.title}</h4>
            </div>

            <div style="background: rgba(255,255,255,0.7); padding: 20px; border-radius: 12px; margin-bottom: 20px; border-left: 4px solid #8b4513;">
                <p style="color: #333; line-height: 1.8; margin-bottom: 15px; font-size: 16px; text-indent: 2em;">
                    ${story.content}
                </p>
                <div style="border-top: 1px solid #ddd; padding-top: 15px; margin-top: 15px;">
                    <p style="color: #666; font-size: 14px; line-height: 1.6;">
                        <strong>功效：</strong>${story.effect}
                    </p>
                </div>
            </div>

            <div style="text-align: center;">
                <button style="
                    background: linear-gradient(135deg, #8b4513, #a0522d);
                    color: white;
                    border: none;
                    border-radius: 12px;
                    padding: 12px 30px;
                    font-size: 16px;
                    cursor: pointer;
                    box-shadow: 0 4px 8px rgba(139, 69, 19, 0.3);
                    transition: all 0.3s ease;
                " onclick="this.closest('.recipe-story-panel').remove()"
                   onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 12px rgba(139, 69, 19, 0.4)'"
                   onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 8px rgba(139, 69, 19, 0.3)'">
                    收下这个配方
                </button>
            </div>
        `;
    } else {
        // 没有故事内容的配方（人数解锁）
        storyContent.innerHTML = `
            <div style="text-align: center;">
                <div style="font-size: 48px; margin-bottom: 20px;">🎉</div>
                <h2 style="color: #8b4513; margin-bottom: 15px;">新配方解锁！</h2>
                <h3 style="color: #2E7D32; margin-bottom: 20px;">${recipeName}</h3>
                <p style="color: #666; line-height: 1.6; margin-bottom: 25px; font-size: 16px;">
                    恭喜您解锁了新的茶饮配方！现在您可以在厨房制作这款特色茶饮了。
                </p>
                <button style="
                    background: linear-gradient(135deg, #8b4513, #a0522d);
                    color: white;
                    border: none;
                    border-radius: 12px;
                    padding: 12px 24px;
                    font-size: 16px;
                    cursor: pointer;
                    box-shadow: 0 4px 8px rgba(139, 69, 19, 0.3);
                " onclick="this.closest('.recipe-story-panel').remove()">
                    太好了！
                </button>
            </div>
        `;
    }

    storyPanel.appendChild(storyContent);
    document.body.appendChild(storyPanel);

    // 8秒后自动关闭（给用户更多时间阅读故事）
    setTimeout(() => {
        if (document.body.contains(storyPanel)) {
            storyPanel.remove();
        }
    }, 8000);
}

// 打开测试页面
function openTestPage() {
    try {
        // 保存当前游戏数据
        localStorage.setItem('teaShopGameData', JSON.stringify(gameData));

        // 计算窗口居中位置
        const width = 1200;
        const height = 800;
        const left = (screen.width - width) / 2;
        const top = (screen.height - height) / 2;

        // 打开测试页面，直接在中间显示
        const windowFeatures = `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes,location=no,menubar=no,toolbar=no,status=no`;
        const testWindow = window.open('全功能测试页面.html', 'teaShopTest', windowFeatures);

        if (testWindow) {
            addMessage('🧪 测试页面已打开');
        } else {
            addMessage('❌ 无法打开测试页面，请检查浏览器弹窗设置');
        }
    } catch (error) {
        addMessage('❌ 打开测试页面失败: ' + error.message);
        console.error('打开测试页面失败:', error);
    }
}

// 打开测试窗口
function openTestWindow() {
    try {
        // 保存当前游戏数据
        saveGame();

        // 计算窗口居中位置
        const width = 900;
        const height = 700;
        const left = (screen.width - width) / 2;
        const top = (screen.height - height) / 2;

        // 打开测试窗口，直接在中间显示
        const windowFeatures = `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes,location=no,menubar=no,toolbar=no,status=no`;
        const testWindow = window.open('测试窗口.html', 'teaShopTestWindow', windowFeatures);

        if (testWindow) {
            addMessage('🔬 测试窗口已打开');
        } else {
            addMessage('❌ 无法打开测试窗口，请检查浏览器弹窗设置');
        }
    } catch (error) {
        addMessage('❌ 打开测试窗口失败: ' + error.message);
        console.error('打开测试窗口失败:', error);
    }
}

// 启动游戏循环
function startGameLoop() {
    debug('启动游戏循环...');

    // 主游戏循环，每秒更新一次
    setInterval(() => {
        if (!isPaused) {
            updateGameState();
        }
    }, 1000);

    // 快速更新循环，每100毫秒更新一次（用于动画和进度条）
    setInterval(() => {
        if (!isPaused) {
            updateFastState();
        }
    }, 100);

    debug('游戏循环已启动');
}

// 更新游戏状态（每秒）
function updateGameState() {
    // 更新天气
    updateWeather();

    // 更新农场
    updateFarm();

    // 更新厨房
    updateKitchen();

    // 更新茶饮温度
    updateTeaTemperature();

    // 更新顾客
    updateCustomer();

    // 生成新顾客
    trySpawnCustomer();

    // 检查人数解锁
    checkCustomerCountUnlocks();
}

// 更新快速状态（每100毫秒）
function updateFastState() {
    // 更新进度条显示
    updateProgressBars();

    // 更新计时器显示
    updateTimers();
}

// 更新天气
function updateWeather() {
    const now = Date.now();
    const elapsed = now - gameData.weatherStartTime;

    if (elapsed >= gameData.weatherDuration) {
        gameData.weatherStartTime = now;

        // 随机选择新天气（遵循季节规则）
        const oldWeather = gameData.currentWeather;
        let newWeather;

        do {
            newWeather = gameData.weathers[Math.floor(Math.random() * gameData.weathers.length)];
        } while (
            // 冬天不能下雨
            (gameData.currentSeason === "冬天" && newWeather === "下雨") ||
            // 非冬天不能下雪
            (gameData.currentSeason !== "冬天" && newWeather === "下雪") ||
            // 不能连续相同天气
            newWeather === oldWeather
        );

        gameData.currentWeather = newWeather;

        // 增加天数
        gameData.currentDay++;
        gameData.daysInSeason++;

        // 如果达到季节变化的天数
        if (gameData.daysInSeason >= gameData.daysPerSeason) {
            gameData.daysInSeason = 0;

            // 更改季节
            const currentSeasonIndex = gameData.seasons.indexOf(gameData.currentSeason);
            gameData.currentSeason = gameData.seasons[(currentSeasonIndex + 1) % gameData.seasons.length];

            addMessage(`🌸 季节已经变为${gameData.currentSeason}了`);
        }

        // 应用天气效果
        applyWeatherEffects();

        updateWeatherDisplay();
        addMessage(`🌤️ 天气变为${gameData.currentWeather}了`);
    }
}

// 应用天气效果
function applyWeatherEffects() {
    // 对每个地块应用天气效果
    gameData.plots.forEach((plot, index) => {
        if (plot.state !== 'empty') {
            // 下雨增加湿度
            if (gameData.currentWeather === "下雨") {
                plot.moisture = Math.min(100, plot.moisture + 20);
                addMessage(`💧 雨水滋润了田地${index + 1}`);
            }
            // 刮风降低湿度
            else if (gameData.currentWeather === "刮风") {
                plot.moisture = Math.max(0, plot.moisture - 10);
                addMessage(`💨 大风使田地${index + 1}的水分蒸发了一些`);
            }
            // 下雪增加湿度和肥力
            else if (gameData.currentWeather === "下雪") {
                plot.moisture = Math.min(100, plot.moisture + 15);
                plot.fertility = Math.min(100, plot.fertility + 10);
                addMessage(`❄️ 雪花为田地${index + 1}带来了养分`);
            }
        }
    });
}

// 更新农场
function updateFarm() {
    let hasChanges = false;

    gameData.plots.forEach((plot, index) => {
        if (plot.state === 'growing') {
            const now = Date.now();
            const elapsed = now - plot.stageStartTime;

            // 检查是否需要进入下一阶段
            if (elapsed >= gameData.stageDuration) {
                plot.growthStage++;
                plot.stageStartTime = now;

                if (plot.growthStage >= gameData.growthStages.length) {
                    // 成熟
                    plot.state = 'mature';
                    addMessage(`🌺 田地${index + 1}的${plot.plantType}成熟了！`);
                }

                hasChanges = true;
            }

            // 消耗湿度和肥力
            if (Math.random() < 0.1) { // 10%概率消耗
                plot.moisture = Math.max(plot.moisture - gameData.moistureConsumption, 0);
                plot.fertility = Math.max(plot.fertility - gameData.fertilityConsumption, 0);
                hasChanges = true;
            }

            // 检查生长条件
            if (plot.moisture < gameData.minMoisture || plot.fertility < gameData.minFertility) {
                // 生长停滞
                if (Math.random() < 0.05) { // 5%概率显示提示
                    addMessage(`⚠️ 田地${index + 1}需要浇水或施肥`);
                }
            }
        }
    });

    if (hasChanges) {
        initFarmGrid();
    }
}

// 更新厨房
function updateKitchen() {
    let hasChanges = false;

    // 更新炉灶
    gameData.stoves.forEach((stove, index) => {
        if (stove.state === 'cooking') {
            const elapsed = Date.now() - stove.startTime;

            if (elapsed >= stove.boilDuration) {
                // 制茶完成
                const teaId = Date.now() + index;
                const newTea = {
                    id: teaId,
                    name: stove.recipe,
                    toppings: []
                };

                gameData.madeTeas.push(newTea);
                gameData.teaTemps[teaId] = 'hot';
                gameData.teaMakeTimes[teaId] = Date.now();

                // 重置炉灶
                stove.state = 'empty';
                stove.recipe = null;
                stove.startTime = 0;

                addMessage(`🍵 ${newTea.name} 制作完成！`);
                hasChanges = true;
            }
        }
    });

    // 更新加工台
    if (gameData.processingBoard.state === 'processing') {
        const elapsed = Date.now() - gameData.processingBoard.startTime;

        if (elapsed >= gameData.processingBoard.duration) {
            // 加工完成
            const recipe = gameData.processingRecipes[gameData.processingBoard.recipe];
            const outputAmount = recipe.output;

            if (!gameData.toppings[gameData.processingBoard.recipe]) {
                gameData.toppings[gameData.processingBoard.recipe] = 0;
            }
            gameData.toppings[gameData.processingBoard.recipe] += outputAmount;

            addMessage(`🎉 加工完成！获得 ${outputAmount} 个 ${gameData.processingBoard.recipe}`);

            // 重置加工台
            gameData.processingBoard.state = 'idle';
            gameData.processingBoard.recipe = null;
            gameData.processingBoard.startTime = 0;
            gameData.processingBoard.duration = 0;

            updateProcessingBoard();
            initProcessingRecipes();
            updateToppingsDisplay();
        }
    }

    if (hasChanges) {
        initKitchen();
        updateTeaDisplay();
    }
}

// 更新茶饮温度
function updateTeaTemperature() {
    let hasChanges = false;

    Object.keys(gameData.teaTemps).forEach(teaId => {
        if (gameData.teaTemps[teaId] === 'hot') {
            const makeTime = gameData.teaMakeTimes[teaId];
            const elapsed = Date.now() - makeTime;

            if (elapsed >= gameData.teaCoolingDuration) {
                gameData.teaTemps[teaId] = 'cold';
                hasChanges = true;
            }
        }
    });

    if (hasChanges) {
        updateTeaDisplay();
    }
}

// 更新顾客
function updateCustomer() {
    if (gameData.customer.active) {
        const elapsed = Date.now() - gameData.customer.arrivalTime;
        const remaining = gameData.customer.patience - elapsed;

        if (remaining <= 0) {
            // 顾客失去耐心离开
            addMessage(`😞 ${gameData.customer.name} 等得不耐烦，离开了...`);
            resetCustomer();
        } else {
            // 更新耐心条
            updateCustomerDisplay();
        }
    }
}

// 尝试生成新顾客
function trySpawnCustomer() {
    if (gameData.customer.active) return;

    const now = Date.now();
    if (now - gameData.lastCustomerTime < gameData.customerSpawnCooldown) return;

    // 30%概率生成顾客
    if (Math.random() < 0.3) {
        spawnRandomCustomer();
        gameData.lastCustomerTime = now;
    }
}

// 生成随机顾客
function spawnRandomCustomer() {
    debug('生成顾客');

    // 30%概率生成特殊顾客（有名字的）
    const isVIP = Math.random() < 0.3;

    // 设置顾客信息
    const customerName = isVIP ? gameData.customerNames[Math.floor(Math.random() * gameData.customerNames.length)] : "普通顾客";

    // 只从已解锁的配方中选择
    if (!gameData.unlockedRecipes || gameData.unlockedRecipes.length === 0) {
        debug('没有可用的配方，使用默认配方');
        gameData.unlockedRecipes = ["五味子饮", "柠檬茶"]; // 确保至少有这两个基础配方
    }
    const teaChoice = gameData.unlockedRecipes[Math.floor(Math.random() * gameData.unlockedRecipes.length)];

    // 修改耐心时间：普通顾客120秒，特殊顾客240秒
    const patience = isVIP ? 240000 : 120000; // VIP 240秒，普通顾客 120秒

    // 更新顾客状态
    gameData.customer = {
        active: true,
        name: customerName,
        isVIP: isVIP,
        teaChoice: teaChoice,
        toppingChoices: [],
        arrivalTime: Date.now(),
        patience: patience,
        maxPatience: patience,
        served: false
    };

    // 随机选择0-2个小料
    const availableToppings = Object.keys(gameData.toppings).filter(t => gameData.toppings[t] > 0);
    const numToppings = Math.floor(Math.random() * 3);
    for (let i = 0; i < numToppings; i++) {
        const topping = availableToppings[Math.floor(Math.random() * availableToppings.length)];
        if (!gameData.customer.toppingChoices.includes(topping)) {
            gameData.customer.toppingChoices.push(topping);
        }
    }

    // 显示顾客消息
    let customerMessage = `${customerName}来到茶铺，想要一杯${teaChoice}`;
    if (gameData.customer.toppingChoices.length > 0) {
        customerMessage += `，加${gameData.customer.toppingChoices.join('、')}`;
    }
    addMessage(customerMessage);

    updateCustomerDisplay();
}

// 检查人数解锁
function checkCustomerCountUnlocks() {
    const customerCount = gameData.servedCustomers || 0;
    const unlockRules = [
        { count: 30, recipe: '桑菊润燥茶' },
        { count: 60, recipe: '桂花酒酿饮' },
        { count: 90, recipe: '蜜桃乌龙冷萃' },
        { count: 120, recipe: '黄芪枸杞茶' },
        { count: 150, recipe: '竹蔗茅根马蹄水' }
    ];

    unlockRules.forEach(rule => {
        if (customerCount >= rule.count && !gameData.unlockedRecipes.includes(rule.recipe)) {
            gameData.unlockedRecipes.push(rule.recipe);
            addMessage(`🎉 服务${rule.count}位顾客，解锁新配方：${rule.recipe}！`);
            showRecipeUnlockStory(rule.recipe);
        }
    });
}

// 更新所有显示
function updateAllDisplays() {
    updateWeatherDisplay();
    updateCustomerDisplay();
    updateCoinsDisplay();
    updateCartBadge();
    initFarmGrid();
    initKitchen();
    updateTeaDisplay();
    updateToppingsDisplay();
}

// 更新天气显示
function updateWeatherDisplay() {
    const weatherIcon = document.getElementById('weather-icon');
    const seasonText = document.getElementById('season-text');
    const dayNumber = document.getElementById('day-number');

    if (weatherIcon) {
        const weatherIcons = {
            '晴天': '☀️',
            '刮风': '💨',
            '下雨': '🌧️',
            '下雪': '❄️',
            '阴天': '☁️'
        };
        weatherIcon.textContent = weatherIcons[gameData.currentWeather] || '☀️';
    }

    if (seasonText) {
        seasonText.textContent = `${gameData.currentSeason} · ${gameData.currentWeather}`;
    }

    if (dayNumber) {
        dayNumber.textContent = gameData.currentDay;
    }
}

// 更新顾客显示
function updateCustomerDisplay() {
    const customerAvatar = document.getElementById('customer-avatar');
    const customerName = document.getElementById('customer-name');
    const customerOrder = document.getElementById('customer-order');
    const patienceBar = document.getElementById('patience-bar');
    const patienceFill = document.getElementById('patience-fill');
    const patienceText = document.getElementById('patience-text');

    if (!gameData.customer.active) {
        if (customerAvatar) customerAvatar.textContent = '😊';
        if (customerName) customerName.textContent = '暂无顾客';
        if (customerOrder) customerOrder.textContent = '等待顾客到来...';
        if (patienceBar) patienceBar.style.display = 'none';
        return;
    }

    // 显示顾客信息
    if (customerAvatar) {
        customerAvatar.textContent = gameData.customer.isVIP ? '👑' : '😊';
    }

    if (customerName) {
        customerName.textContent = gameData.customer.name + (gameData.customer.isVIP ? ' (VIP)' : '');
    }

    if (customerOrder) {
        const toppingText = gameData.customer.toppingChoices.length > 0
            ? `，要加${gameData.customer.toppingChoices.join('、')}`
            : '';
        customerOrder.textContent = `想要${gameData.customer.teaChoice}${toppingText}`;
    }

    // 更新耐心条
    if (patienceBar && patienceFill && patienceText) {
        patienceBar.style.display = 'block';

        const elapsed = Date.now() - gameData.customer.arrivalTime;
        const remaining = Math.max(0, gameData.customer.patience - elapsed);
        const percentage = (remaining / gameData.customer.maxPatience) * 100;

        patienceFill.style.width = `${percentage}%`;
        patienceText.textContent = `耐心：${Math.round(percentage)}%`;

        // 根据耐心值改变颜色
        if (percentage > 60) {
            patienceFill.style.background = 'linear-gradient(90deg, #4CAF50, #81C784)';
        } else if (percentage > 30) {
            patienceFill.style.background = 'linear-gradient(90deg, #FFD54F, #FFC107)';
        } else {
            patienceFill.style.background = 'linear-gradient(90deg, #F44336, #E57373)';
        }
    }
}

// 更新铜板显示
function updateCoinsDisplay() {
    const coinCount = document.getElementById('coin-count');
    if (coinCount) {
        coinCount.textContent = gameData.coins;
    }
}

// 更新进度条
function updateProgressBars() {
    // 更新炉灶进度条
    gameData.stoves.forEach((stove, index) => {
        if (stove.state === 'cooking') {
            const stoveCard = document.querySelector(`[data-stove-id="${index}"]`);
            if (stoveCard) {
                const progressFill = stoveCard.querySelector('.progress-fill');
                if (progressFill) {
                    const progress = getStoveProgress(stove);
                    progressFill.style.width = `${progress}%`;
                }
            }
        }
    });
}

// 更新计时器
function updateTimers() {
    // 更新田地计时器
    gameData.plots.forEach((plot, index) => {
        if (plot.state === 'growing') {
            const plotCard = document.querySelector(`[data-plot-id="${index}"]`);
            if (plotCard) {
                const timer = plotCard.querySelector('.plot-timer');
                if (timer) {
                    const elapsed = Date.now() - plot.stageStartTime;
                    const remaining = Math.max(0, gameData.stageDuration - elapsed);
                    const seconds = Math.ceil(remaining / 1000);

                    if (seconds > 0) {
                        const minutes = Math.floor(seconds / 60);
                        const secs = seconds % 60;
                        timer.textContent = `⏰ ${minutes}:${secs.toString().padStart(2, '0')}`;
                    } else {
                        timer.textContent = '✨ 即将成熟！';
                    }
                }
            }
        }
    });

    // 更新炉灶计时器
    gameData.stoves.forEach((stove, index) => {
        if (stove.state === 'cooking') {
            const stoveCard = document.querySelector(`[data-stove-id="${index}"]`);
            if (stoveCard) {
                const timer = stoveCard.querySelector('.cooking-timer');
                if (timer) {
                    const elapsed = Date.now() - stove.startTime;
                    const remaining = Math.max(0, stove.boilDuration - elapsed);
                    const seconds = Math.ceil(remaining / 1000);

                    if (seconds > 0) {
                        const minutes = Math.floor(seconds / 60);
                        const secs = seconds % 60;
                        timer.textContent = `⏰ ${minutes}:${secs.toString().padStart(2, '0')}`;
                    } else {
                        timer.textContent = '✨ 即将完成！';
                    }
                }
            }
        }
    });

    // 更新加工台计时器
    if (gameData.processingBoard.state === 'processing') {
        updateProcessingBoard();
    }
}

// 显示篮子面板（种植选择）
function showBasketPanel(plotIndex = null) {
    const panel = document.createElement('div');
    panel.className = 'basket-panel';
    panel.style.cssText = `
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        background: white;
        border-radius: 20px;
        padding: 20px;
        box-shadow: 0 8px 25px rgba(76, 175, 80, 0.25);
        z-index: 1001;
        max-width: 90vw;
        max-height: 80vh;
        overflow-y: auto;
        min-width: 300px;
    `;

    const availableSeeds = Object.entries(gameData.inventory).filter(([name, count]) => count > 0);

    panel.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h3 style="margin: 0; color: #2E7D32;">🧺 选择种子</h3>
            <button class="close-basket-panel" style="background: none; border: none; font-size: 24px; cursor: pointer;">×</button>
        </div>
        <div style="margin-bottom: 15px; color: #666; font-size: 14px;">
            ${plotIndex !== null ? `为田地${plotIndex + 1}选择种子：` : '选择要种植的种子：'}
        </div>
        <div class="seed-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px;">
            ${availableSeeds.map(([seedName, count]) => `
                <button class="seed-option" data-seed="${seedName}" style="
                    background: #E8F5E8;
                    border: 2px solid #81C784;
                    border-radius: 12px;
                    padding: 12px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    text-align: center;
                ">
                    <div style="font-size: 24px; margin-bottom: 8px;">🌰</div>
                    <div style="font-size: 12px; font-weight: 500; margin-bottom: 4px;">${seedName}</div>
                    <div style="font-size: 10px; color: #666;">x${count}</div>
                </button>
            `).join('')}
        </div>
        ${availableSeeds.length === 0 ? '<div style="text-align: center; color: #999; padding: 20px;">没有可种植的种子</div>' : ''}
    `;

    document.body.appendChild(panel);

    // 添加事件监听器
    panel.querySelector('.close-basket-panel').addEventListener('click', () => {
        document.body.removeChild(panel);
    });

    panel.querySelectorAll('.seed-option').forEach(btn => {
        btn.addEventListener('click', () => {
            const seedName = btn.dataset.seed;

            if (plotIndex !== null) {
                // 直接种植到指定田地
                plantSeed(plotIndex, seedName);
            } else {
                // 选择田地种植
                selectPlotForPlanting(seedName);
            }

            document.body.removeChild(panel);
        });

        btn.addEventListener('mouseenter', () => {
            btn.style.background = '#4CAF50';
            btn.style.color = 'white';
        });

        btn.addEventListener('mouseleave', () => {
            btn.style.background = '#E8F5E8';
            btn.style.color = '#2E7D32';
        });
    });
}

// 种植种子
function plantSeed(plotIndex, seedName) {
    const plot = gameData.plots[plotIndex];

    if (plot.state !== 'empty') {
        addMessage('❌ 这块田地已经种植了作物');
        return;
    }

    if (!gameData.inventory[seedName] || gameData.inventory[seedName] <= 0) {
        addMessage('❌ 没有足够的种子');
        return;
    }

    // 消耗种子
    gameData.inventory[seedName]--;

    // 种植
    plot.state = 'growing';
    plot.growthStage = 0;
    plot.stageStartTime = Date.now();
    plot.plantType = seedName;

    addMessage(`🌱 在田地${plotIndex + 1}种植了${seedName}`);

    // 刷新显示
    initFarmGrid();
}

// 选择田地种植
function selectPlotForPlanting(seedName) {
    gameData.selectedSeedForPlanting = seedName;
    addMessage(`🌰 已选择${seedName}，请点击空田地进行种植`);

    // 高亮显示空田地
    document.querySelectorAll('.plot-card.empty').forEach(card => {
        card.classList.add('selected');

        const clickHandler = () => {
            const plotIndex = parseInt(card.dataset.plotId);
            plantSeed(plotIndex, seedName);

            // 移除高亮和事件监听器
            document.querySelectorAll('.plot-card').forEach(c => {
                c.classList.remove('selected');
                c.removeEventListener('click', clickHandler);
            });

            gameData.selectedSeedForPlanting = null;
        };

        card.addEventListener('click', clickHandler);
    });
}

// 保存游戏
function saveGame() {
    try {
        const saveData = {
            ...gameData,
            saveTime: Date.now(),
            version: '1.0'
        };

        localStorage.setItem('cuteTeaShop_save', JSON.stringify(saveData));
        debug('游戏已保存到本地存储');
        return true;
    } catch (error) {
        debug('保存游戏失败: ' + error.message);
        return false;
    }
}

// 加载游戏
function loadGame() {
    try {
        const saveData = localStorage.getItem('cuteTeaShop_save');
        if (!saveData) {
            debug('没有找到保存的游戏数据');
            return false;
        }

        const parsedData = JSON.parse(saveData);

        // 合并保存的数据到游戏数据
        Object.assign(gameData, parsedData);

        // 更新所有显示
        updateAllDisplays();

        debug('游戏已从本地存储加载');
        return true;
    } catch (error) {
        debug('加载游戏失败: ' + error.message);
        return false;
    }
}

// 显示商店面板
function showShopPanel() {
    const panel = document.createElement('div');
    panel.className = 'shop-panel';
    panel.style.cssText = `
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        background: white;
        border-radius: 20px;
        box-shadow: 0 8px 25px rgba(76, 175, 80, 0.25);
        z-index: 1001;
        max-width: 90vw;
        max-height: 80vh;
        overflow: hidden;
        min-width: 400px;
    `;

    panel.innerHTML = `
        <div class="panel-header" style="background: #4CAF50; color: white; padding: 16px 20px; display: flex; justify-content: space-between; align-items: center;">
            <h3 style="margin: 0; font-size: 18px;">🏪 茶铺商店</h3>
            <button class="close-shop-panel" style="background: none; border: none; color: white; font-size: 24px; cursor: pointer; padding: 0; width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">×</button>
        </div>
        <div class="panel-content" style="padding: 20px; max-height: 60vh; overflow-y: auto;">
            <div style="margin-bottom: 20px;">
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                    <span style="font-size: 20px; margin-right: 8px;">🪙</span>
                    <span style="font-size: 16px; font-weight: bold; color: #2E7D32;">当前铜板：${gameData.coins}</span>
                </div>
            </div>

            <div class="shop-categories">
                <div class="shop-category">
                    <h4 style="color: #4CAF50; margin-bottom: 15px; border-bottom: 2px solid #E8F5E8; padding-bottom: 8px;">🌿 基础材料</h4>
                    <div class="shop-items" style="display: grid; grid-template-columns: 1fr; gap: 10px;">
                        ${Object.entries(gameData.shopItems).map(([itemName, itemData]) => `
                            <div class="shop-item" style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: #F8F9FA; border-radius: 12px; border: 1px solid #E9ECEF;">
                                <div style="display: flex; align-items: center;">
                                    <span style="font-size: 20px; margin-right: 12px;">${getToppingIcon(itemName)}</span>
                                    <div>
                                        <div style="font-weight: 500; color: #2E7D32;">${itemName}</div>
                                        <div style="font-size: 12px; color: #666;">价格：${itemData.price} 铜板</div>
                                    </div>
                                </div>
                                <button class="buy-item" data-item="${itemName}" data-price="${itemData.price}" style="
                                    background: ${gameData.coins >= itemData.price ? '#4CAF50' : '#CCC'};
                                    color: white;
                                    border: none;
                                    border-radius: 8px;
                                    padding: 6px 12px;
                                    font-size: 12px;
                                    cursor: ${gameData.coins >= itemData.price ? 'pointer' : 'not-allowed'};
                                    transition: all 0.3s ease;
                                " ${gameData.coins < itemData.price ? 'disabled' : ''}>
                                    购买
                                </button>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="shop-category" style="margin-top: 20px;">
                    <h4 style="color: #4CAF50; margin-bottom: 15px; border-bottom: 2px solid #E8F5E8; padding-bottom: 8px;">🌰 种子商店</h4>
                    <div class="shop-items" style="display: grid; grid-template-columns: 1fr; gap: 10px;">
                        ${MATERIALS.slice(0, 10).map(material => `
                            <div class="shop-item" style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: #F8F9FA; border-radius: 12px; border: 1px solid #E9ECEF;">
                                <div style="display: flex; align-items: center;">
                                    <span style="font-size: 20px; margin-right: 12px;">🌰</span>
                                    <div>
                                        <div style="font-weight: 500; color: #2E7D32;">${material}种子</div>
                                        <div style="font-size: 12px; color: #666;">价格：2 铜板</div>
                                    </div>
                                </div>
                                <button class="buy-seed" data-seed="${material}" data-price="2" style="
                                    background: ${gameData.coins >= 2 ? '#4CAF50' : '#CCC'};
                                    color: white;
                                    border: none;
                                    border-radius: 8px;
                                    padding: 6px 12px;
                                    font-size: 12px;
                                    cursor: ${gameData.coins >= 2 ? 'pointer' : 'not-allowed'};
                                    transition: all 0.3s ease;
                                " ${gameData.coins < 2 ? 'disabled' : ''}>
                                    购买
                                </button>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(panel);

    // 添加事件监听器
    panel.querySelector('.close-shop-panel').addEventListener('click', () => {
        document.body.removeChild(panel);
    });

    // 购买物品
    panel.querySelectorAll('.buy-item').forEach(btn => {
        btn.addEventListener('click', () => {
            const itemName = btn.dataset.item;
            const price = parseInt(btn.dataset.price);

            if (gameData.coins >= price) {
                gameData.coins -= price;

                if (!gameData.toppings[itemName]) {
                    gameData.toppings[itemName] = 0;
                }
                gameData.toppings[itemName] += 1;

                addMessage(`🛒 购买了 ${itemName}，花费 ${price} 铜板`);

                // 更新显示
                updateCoinsDisplay();
                updateToppingsDisplay();

                // 关闭面板并重新打开以更新价格显示
                document.body.removeChild(panel);
                showShopPanel();
            }
        });
    });

    // 购买种子
    panel.querySelectorAll('.buy-seed').forEach(btn => {
        btn.addEventListener('click', () => {
            const seedName = btn.dataset.seed;
            const price = parseInt(btn.dataset.price);

            if (gameData.coins >= price) {
                gameData.coins -= price;

                if (!gameData.inventory[seedName]) {
                    gameData.inventory[seedName] = 0;
                }
                gameData.inventory[seedName] += 1;

                addMessage(`🛒 购买了 ${seedName}种子，花费 ${price} 铜板`);

                // 更新显示
                updateCoinsDisplay();

                // 关闭面板并重新打开以更新价格显示
                document.body.removeChild(panel);
                showShopPanel();
            }
        });
    });
}

// 显示配方大全
function showRecipeBook() {
    const panel = document.createElement('div');
    panel.className = 'recipe-panel';
    panel.style.cssText = `
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        background: white;
        border-radius: 20px;
        box-shadow: 0 8px 25px rgba(76, 175, 80, 0.25);
        z-index: 1001;
        max-width: 90vw;
        max-height: 80vh;
        overflow: hidden;
        min-width: 450px;
    `;

    const allRecipes = [
        { name: '五味子饮', ingredients: ['五味子'], unlockType: '基础配方' },
        { name: '柠檬茶', ingredients: ['柠檬'], unlockType: '基础配方' },
        { name: '洛神玫瑰饮', ingredients: ['洛神花', '玫瑰花', '山楂'], unlockType: '凌小路解锁' },
        { name: '桂圆红枣茶', ingredients: ['桂圆', '红枣', '枸杞'], unlockType: '花花解锁' },
        { name: '焦香大麦茶', ingredients: ['大麦'], unlockType: '江飞飞解锁' },
        { name: '三花决明茶', ingredients: ['菊花', '金银花', '决明子', '枸杞'], unlockType: '江三解锁' },
        { name: '薄荷甘草凉茶', ingredients: ['薄荷', '甘草'], unlockType: '江四解锁' },
        { name: '陈皮姜米茶', ingredients: ['陈皮', '生姜'], unlockType: '池云旗解锁' },
        { name: '冬瓜荷叶饮', ingredients: ['冬瓜', '荷叶', '薏米'], unlockType: '江潮解锁' },
        { name: '古法酸梅汤', ingredients: ['乌梅', '山楂', '陈皮', '甘草', '桂花'], unlockType: '池惊暮解锁' },
        { name: '小吊梨汤', ingredients: ['雪花梨', '银耳', '话梅', '枸杞'], unlockType: '江敕封解锁' },
        { name: '桑菊润燥茶', ingredients: ['桑叶', '杭白菊'], unlockType: '30人解锁' },
        { name: '桂花酒酿饮', ingredients: ['桂花', '酒酿'], unlockType: '60人解锁' },
        { name: '蜜桃乌龙冷萃', ingredients: ['水蜜桃', '乌龙茶包'], unlockType: '90人解锁' },
        { name: '黄芪枸杞茶', ingredients: ['黄芪', '枸杞'], unlockType: '120人解锁' },
        { name: '竹蔗茅根马蹄水', ingredients: ['甘蔗', '白茅根', '马蹄'], unlockType: '150人解锁' }
    ];

    panel.innerHTML = `
        <div class="panel-header" style="background: #4CAF50; color: white; padding: 16px 20px; display: flex; justify-content: space-between; align-items: center;">
            <h3 style="margin: 0; font-size: 18px;">📖 配方大全</h3>
            <button class="close-recipe-panel" style="background: none; border: none; color: white; font-size: 24px; cursor: pointer; padding: 0; width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">×</button>
        </div>
        <div class="panel-content" style="padding: 20px; max-height: 60vh; overflow-y: auto;">
            <div style="margin-bottom: 15px; color: #666; font-size: 14px;">
                已解锁配方：${gameData.unlockedRecipes.length} / ${allRecipes.length}
            </div>

            <div class="recipe-list" style="display: grid; gap: 12px;">
                ${allRecipes.map(recipe => {
                    const isUnlocked = gameData.unlockedRecipes.includes(recipe.name);
                    return `
                        <div class="recipe-item" style="
                            padding: 15px;
                            background: ${isUnlocked ? '#E8F5E8' : '#F5F5F5'};
                            border-radius: 12px;
                            border: 2px solid ${isUnlocked ? '#81C784' : '#DDD'};
                            opacity: ${isUnlocked ? '1' : '0.6'};
                        ">
                            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                                <div>
                                    <div style="font-weight: bold; color: ${isUnlocked ? '#2E7D32' : '#666'}; margin-bottom: 4px;">
                                        ${isUnlocked ? '🍵' : '🔒'} ${recipe.name}
                                    </div>
                                    <div style="font-size: 12px; color: #666;">
                                        ${recipe.unlockType}
                                    </div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="font-size: 12px; color: #666; margin-bottom: 4px;">所需材料：</div>
                                    <div style="font-size: 11px; color: ${isUnlocked ? '#4CAF50' : '#999'};">
                                        ${isUnlocked ? recipe.ingredients.join('、') : '???'}
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('')}
            </div>
        </div>
    `;

    document.body.appendChild(panel);

    // 添加事件监听器
    panel.querySelector('.close-recipe-panel').addEventListener('click', () => {
        document.body.removeChild(panel);
    });
}

// 显示测试面板
function showTestPanel() {
    const panel = document.createElement('div');
    panel.className = 'test-panel';
    panel.style.cssText = `
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        background: white;
        border-radius: 20px;
        box-shadow: 0 8px 25px rgba(76, 175, 80, 0.25);
        z-index: 1001;
        max-width: 90vw;
        max-height: 80vh;
        overflow: hidden;
        min-width: 400px;
    `;

    panel.innerHTML = `
        <div class="panel-header" style="background: #4CAF50; color: white; padding: 16px 20px; display: flex; justify-content: space-between; align-items: center;">
            <h3 style="margin: 0; font-size: 18px;">🧪 测试模式</h3>
            <button class="close-test-panel" style="background: none; border: none; color: white; font-size: 24px; cursor: pointer; padding: 0; width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">×</button>
        </div>
        <div class="panel-content" style="padding: 20px; max-height: 60vh; overflow-y: auto;">
            <div style="margin-bottom: 20px;">
                <h4 style="color: #4CAF50; margin-bottom: 10px;">🎮 游戏测试</h4>
                <div style="display: grid; gap: 10px;">
                    <button class="test-btn" data-action="add-coins" style="background: #4CAF50; color: white; border: none; border-radius: 8px; padding: 10px; cursor: pointer;">
                        💰 添加1000铜板
                    </button>
                    <button class="test-btn" data-action="add-materials" style="background: #4CAF50; color: white; border: none; border-radius: 8px; padding: 10px; cursor: pointer;">
                        🌿 添加所有材料
                    </button>
                    <button class="test-btn" data-action="add-toppings" style="background: #4CAF50; color: white; border: none; border-radius: 8px; padding: 10px; cursor: pointer;">
                        🍯 添加所有小料
                    </button>
                    <button class="test-btn" data-action="unlock-recipes" style="background: #4CAF50; color: white; border: none; border-radius: 8px; padding: 10px; cursor: pointer;">
                        📖 解锁所有配方
                    </button>
                    <button class="test-btn" data-action="spawn-customer" style="background: #4CAF50; color: white; border: none; border-radius: 8px; padding: 10px; cursor: pointer;">
                        👑 生成VIP顾客
                    </button>
                    <button class="test-btn" data-action="add-teas" style="background: #4CAF50; color: white; border: none; border-radius: 8px; padding: 10px; cursor: pointer;">
                        🍵 添加所有茶饮
                    </button>
                    <button class="test-btn" data-action="fast-grow" style="background: #4CAF50; color: white; border: none; border-radius: 8px; padding: 10px; cursor: pointer;">
                        🌱 快速成熟农作物
                    </button>
                    <button class="test-btn" data-action="open-test-page" style="background: #2196F3; color: white; border: none; border-radius: 8px; padding: 10px; cursor: pointer;">
                        🧪 打开全功能测试页面
                    </button>
                    <button class="test-btn" data-action="reset-game" style="background: #F44336; color: white; border: none; border-radius: 8px; padding: 10px; cursor: pointer;">
                        🔄 重置游戏
                    </button>
                </div>
            </div>

            <div style="margin-bottom: 20px;">
                <h4 style="color: #4CAF50; margin-bottom: 10px;">📊 游戏状态</h4>
                <div style="background: #F8F9FA; padding: 15px; border-radius: 8px; font-size: 14px; line-height: 1.6;">
                    <div><strong>铜板：</strong>${gameData.coins}</div>
                    <div><strong>已服务顾客：</strong>${gameData.servedCustomers || 0}</div>
                    <div><strong>已解锁配方：</strong>${gameData.unlockedRecipes.length}/16</div>
                    <div><strong>当前季节：</strong>${gameData.currentSeason}</div>
                    <div><strong>当前天气：</strong>${gameData.currentWeather}</div>
                    <div><strong>游戏天数：</strong>${gameData.currentDay}</div>
                    <div><strong>制作的茶饮：</strong>${gameData.madeTeas.length}</div>
                    <div><strong>当前顾客：</strong>${gameData.customer.active ? gameData.customer.name : '无'}</div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(panel);

    // 添加事件监听器
    panel.querySelector('.close-test-panel').addEventListener('click', () => {
        document.body.removeChild(panel);
    });

    // 测试按钮事件
    panel.querySelectorAll('.test-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const action = btn.dataset.action;
            executeTestAction(action);

            // 关闭面板并重新打开以更新状态显示
            document.body.removeChild(panel);
            showTestPanel();
        });
    });
}

// 执行测试操作
function executeTestAction(action) {
    switch (action) {
        case 'add-coins':
            gameData.coins += 1000;
            addMessage('💰 添加了1000铜板');
            updateCoinsDisplay();
            break;

        case 'add-materials':
            MATERIALS.forEach(material => {
                gameData.inventory[material] = (gameData.inventory[material] || 0) + 10;
            });
            addMessage('🌿 添加了所有材料各10个');
            break;

        case 'add-toppings':
            Object.keys(gameData.toppings).forEach(topping => {
                gameData.toppings[topping] = 10;
            });
            addMessage('🍯 添加了所有小料各10个');
            updateToppingsDisplay();
            break;

        case 'unlock-recipes':
            const allRecipes = [
                '洛神玫瑰饮', '桂圆红枣茶', '焦香大麦茶', '三花决明茶', '薄荷甘草凉茶',
                '陈皮姜米茶', '冬瓜荷叶饮', '古法酸梅汤', '小吊梨汤',
                '桑菊润燥茶', '桂花酒酿饮', '蜜桃乌龙冷萃', '黄芪枸杞茶', '竹蔗茅根马蹄水'
            ];
            allRecipes.forEach(recipe => {
                if (!gameData.unlockedRecipes.includes(recipe)) {
                    gameData.unlockedRecipes.push(recipe);
                }
            });
            addMessage('📖 解锁了所有配方');
            break;

        case 'open-test-page':
            openTestPage();
            break;

        case 'spawn-customer':
            if (!gameData.customer.active) {
                spawnTestCustomer();
            } else {
                addMessage('❌ 已有顾客在等待');
            }
            break;

        case 'add-teas':
            const completeTeas = gameData.unlockedRecipes.slice(0, 5);
            completeTeas.forEach((tea, index) => {
                const teaId = Date.now() + index;
                const newTea = {
                    id: teaId,
                    name: tea,
                    toppings: []
                };
                gameData.madeTeas.push(newTea);
                gameData.teaTemps[teaId] = 'hot';
                gameData.teaMakeTimes[teaId] = Date.now();
            });
            addMessage(`🍵 添加了${completeTeas.length}杯茶饮`);
            updateTeaDisplay();
            break;

        case 'fast-grow':
            gameData.plots.forEach((plot, index) => {
                if (plot.state === 'growing') {
                    plot.state = 'mature';
                    addMessage(`🌺 田地${index + 1}的作物快速成熟`);
                }
            });
            initFarmGrid();
            break;

        case 'reset-game':
            if (confirm('确定要重置游戏吗？这将清除所有进度！')) {
                localStorage.removeItem('cuteTeaShop_save');
                location.reload();
            }
            break;
    }
}

// 生成测试顾客
function spawnTestCustomer() {
    const vipCustomers = ['凌小路', '花花', '江飞飞', '江三', '江四', '池云旗', '江潮', '池惊暮', '江敕封'];
    const customerName = vipCustomers[Math.floor(Math.random() * vipCustomers.length)];
    const teaChoice = gameData.unlockedRecipes[Math.floor(Math.random() * gameData.unlockedRecipes.length)];

    gameData.customer = {
        active: true,
        name: customerName,
        isVIP: true,
        teaChoice: teaChoice,
        toppingChoices: [],
        arrivalTime: Date.now(),
        patience: 300000, // 5分钟
        maxPatience: 300000,
        served: false
    };

    addMessage(`👑 测试VIP顾客 ${customerName} 来了，想要${teaChoice}`);
    updateCustomerDisplay();
}

// 自动保存游戏（每30秒）
setInterval(() => {
    if (!isPaused) {
        saveGame();
        debug('自动保存游戏');
    }
}, 30000);

// 页面卸载时保存游戏
window.addEventListener('beforeunload', () => {
    saveGame();
});

// 页面可见性变化时暂停/恢复游戏
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        isPaused = true;
        debug('页面隐藏，游戏暂停');
    } else {
        isPaused = false;
        debug('页面显示，游戏恢复');
    }
});

// 游戏启动时尝试加载保存的游戏
document.addEventListener('DOMContentLoaded', () => {
    // 延迟加载，确保游戏初始化完成
    setTimeout(() => {
        if (loadGame()) {
            addMessage('📁 已加载保存的游戏进度');
        }
    }, 1000);
});

// 显示库存面板
function showInventoryPanel() {
    const panel = document.createElement('div');
    panel.className = 'inventory-panel';
    panel.style.cssText = `
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        background: white;
        border-radius: 20px;
        box-shadow: 0 8px 25px rgba(76, 175, 80, 0.25);
        z-index: 1001;
        max-width: 90vw;
        max-height: 80vh;
        overflow: hidden;
        min-width: 400px;
    `;

    panel.innerHTML = `
        <div class="panel-header" style="background: #4CAF50; color: white; padding: 16px 20px; display: flex; justify-content: space-between; align-items: center;">
            <h3 style="margin: 0; font-size: 18px;">🧺 库存篮子</h3>
            <button class="close-inventory-panel" style="background: none; border: none; color: white; font-size: 24px; cursor: pointer; padding: 0; width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">×</button>
        </div>
        <div class="panel-content" style="padding: 20px; max-height: 60vh; overflow-y: auto;">
            <div style="margin-bottom: 20px;">
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                    <span style="font-size: 20px; margin-right: 8px;">🪙</span>
                    <span style="font-size: 16px; font-weight: bold; color: #2E7D32;">当前铜板：${gameData.coins}</span>
                </div>
            </div>

            <div class="inventory-categories">
                <div class="inventory-category">
                    <h4 style="color: #4CAF50; margin-bottom: 15px; border-bottom: 2px solid #E8F5E8; padding-bottom: 8px;">🌿 种植材料</h4>
                    <div class="inventory-items" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px;">
                        ${Object.entries(gameData.inventory).filter(([name, count]) => count > 0).map(([itemName, count]) => `
                            <div class="inventory-item" style="background: #F8F9FA; border-radius: 12px; padding: 12px; text-align: center; border: 1px solid #E9ECEF;">
                                <div style="font-size: 24px; margin-bottom: 8px;">🌰</div>
                                <div style="font-weight: 500; color: #2E7D32; margin-bottom: 4px; font-size: 12px;">${itemName}</div>
                                <div style="font-size: 14px; color: #4CAF50; font-weight: bold;">x${count}</div>
                            </div>
                        `).join('')}
                        ${Object.entries(gameData.inventory).filter(([name, count]) => count > 0).length === 0 ?
                            '<div style="text-align: center; color: #999; padding: 20px; grid-column: 1 / -1;">暂无种植材料</div>' : ''}
                    </div>
                </div>

                <div class="inventory-category" style="margin-top: 20px;">
                    <h4 style="color: #4CAF50; margin-bottom: 15px; border-bottom: 2px solid #E8F5E8; padding-bottom: 8px;">🍯 小料库存</h4>
                    <div class="inventory-items" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px;">
                        ${Object.entries(gameData.toppings).filter(([name, count]) => count > 0).map(([toppingName, count]) => `
                            <div class="inventory-item" style="background: #F8F9FA; border-radius: 12px; padding: 12px; text-align: center; border: 1px solid #E9ECEF;">
                                <div style="font-size: 24px; margin-bottom: 8px;">${getToppingIcon(toppingName)}</div>
                                <div style="font-weight: 500; color: #2E7D32; margin-bottom: 4px; font-size: 12px;">${toppingName}</div>
                                <div style="font-size: 14px; color: #4CAF50; font-weight: bold;">x${count}</div>
                            </div>
                        `).join('')}
                        ${Object.entries(gameData.toppings).filter(([name, count]) => count > 0).length === 0 ?
                            '<div style="text-align: center; color: #999; padding: 20px; grid-column: 1 / -1;">暂无小料库存</div>' : ''}
                    </div>
                </div>

                <div class="inventory-category" style="margin-top: 20px;">
                    <h4 style="color: #4CAF50; margin-bottom: 15px; border-bottom: 2px solid #E8F5E8; padding-bottom: 8px;">🍵 制作的茶饮</h4>
                    <div class="inventory-items" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
                        ${gameData.madeTeas.map((tea, index) => {
                            const isHot = gameData.teaTemps[tea.id] === 'hot';
                            return `
                                <div class="inventory-item" style="background: #F8F9FA; border-radius: 12px; padding: 12px; text-align: center; border: 1px solid #E9ECEF;">
                                    <div style="font-size: 24px; margin-bottom: 8px;">${isHot ? '🍵' : '🧊'}</div>
                                    <div style="font-weight: 500; color: #2E7D32; margin-bottom: 4px; font-size: 12px;">${tea.name}</div>
                                    <div style="font-size: 10px; color: #666; margin-bottom: 4px;">${isHot ? '热茶' : '冰茶'}</div>
                                    ${tea.toppings && tea.toppings.length > 0 ?
                                        `<div style="font-size: 10px; color: #4CAF50;">+${tea.toppings.join('、')}</div>` :
                                        '<div style="font-size: 10px; color: #999;">无小料</div>'}
                                </div>
                            `;
                        }).join('')}
                        ${gameData.madeTeas.length === 0 ?
                            '<div style="text-align: center; color: #999; padding: 20px; grid-column: 1 / -1;">暂无制作的茶饮</div>' : ''}
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(panel);

    // 添加事件监听器
    panel.querySelector('.close-inventory-panel').addEventListener('click', () => {
        document.body.removeChild(panel);
    });
}

debug('🍵 可爱茶铺脚本加载完成！');