<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>🍵 可爱茶铺 - 古风经营游戏</title>
    <link rel="stylesheet" href="cute_tea_shop.css">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#4CAF50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="可爱茶铺">
    <link rel="icon" href="icon.svg" type="image/svg+xml">
    <script>
        // 注册Service Worker以支持离线使用
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/service-worker.js')
                    .then(registration => {
                        console.log('ServiceWorker注册成功:', registration.scope);
                    })
                    .catch(error => {
                        console.log('ServiceWorker注册失败:', error);
                    });
            });
        }
    </script>
</head>
<body>
    <div class="container">
        <!-- 🏠 可爱顶部区域 -->
        <header class="cute-header">
            <div class="coins-bubble">
                <span class="coin-icon">🪙</span>
                <span class="coin-count" id="cute-coins-count">100</span>
            </div>
            <div class="title-container">
                <h1 class="cute-title">🍵 可爱茶铺</h1>
                <div class="subtitle">古风经营小游戏</div>
            </div>
            <button class="menu-bubble" id="cute-menu-btn">
                <span class="menu-icon">☰</span>
            </button>
        </header>

        <!-- 🎮 菜单面板 -->
        <div class="menu-panel" id="cute-menu-panel">
            <div class="menu-content">
                <div class="menu-title">🎮 游戏菜单</div>
                <div class="menu-buttons">
                    <button id="cute-save-manager" class="menu-btn save-btn">
                        <span class="btn-icon">💾</span>
                        <span class="btn-text">存档管理</span>
                    </button>
                    <button id="cute-recipe-button" class="menu-btn recipe-btn">
                        <span class="btn-icon">📜</span>
                        <span class="btn-text">查看配方</span>
                    </button>
                    <button id="cute-collection-button" class="menu-btn collection-btn">
                        <span class="btn-icon">🎴</span>
                        <span class="btn-text">集卡册</span>
                    </button>
                    <button id="cute-unlock-progress-button" class="menu-btn progress-btn">
                        <span class="btn-icon">📈</span>
                        <span class="btn-text">解锁进度</span>
                    </button>
                    <button id="cute-special-customer-test-button" class="menu-btn test-btn">
                        <span class="btn-icon">🧪</span>
                        <span class="btn-text">测试模式</span>
                    </button>
                    <button id="cute-pause-btn" class="menu-btn pause-btn">
                        <span class="btn-icon">⏸️</span>
                        <span class="btn-text">暂停游戏</span>
                    </button>
                </div>
                <button id="cute-close-menu" class="close-menu-btn">✕</button>
            </div>
        </div>

        <!-- 💬 可爱信息卡片区域 -->
        <section class="info-cards">
            <div class="info-card weather-card">
                <div class="card-header">
                    <span class="weather-icon" id="cute-weather">☀️</span>
                    <span class="season-text">
                        <span id="cute-season">春天</span> · 晴天
                    </span>
                </div>
                <div class="day-info">第 <span class="day-number" id="cute-day">1</span> 天</div>
                <div class="quick-actions-mini">
                    <button id="cute-basket-btn" class="action-mini basket-mini" title="查看篮子">🧺</button>
                    <button id="cute-buy-seed-status" class="action-mini shop-mini" title="打开商店">🏪</button>
                </div>
            </div>

            <div class="info-card customer-card">
                <div class="customer-avatar">👧</div>
                <div class="customer-info">
                    <div class="customer-name" id="cute-customer-name">暂无顾客</div>
                    <div class="customer-order">
                        想要：<span id="cute-customer-tea">-</span>
                        <br>加料：<span id="cute-customer-toppings">-</span>
                    </div>
                    <div class="patience-bar">
                        <div class="patience-fill" id="cute-patience-bar"></div>
                        <span class="patience-text">耐心：<span id="cute-patience-timer">-</span>秒</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 消息显示区域 -->
        <div class="message-bubble" id="cute-message-bubble">
            <span class="message-icon">💬</span>
            <div class="message-content">
                <div class="message">欢迎来到可爱茶铺!</div>
            </div>
        </div>

        <!-- 🎯 可爱选项卡导航 -->
        <nav class="cute-tabs">
            <button class="tab-button active game-tab" data-tab="farm-tab">
                <span class="tab-icon">🌱</span>
                <span class="tab-text">种植</span>
            </button>
            <button class="tab-button game-tab" data-tab="kitchen-tab">
                <span class="tab-icon">🍳</span>
                <span class="tab-text">厨房</span>
            </button>
            <button class="tab-button game-tab" data-tab="tea-tab">
                <span class="tab-icon">🍵</span>
                <span class="tab-text">茶摊</span>
            </button>
        </nav>

        <!-- 🌱 种植页面 -->
        <section class="tab-content active game-content" id="farm-tab">
            <div class="section-title">
                <span class="title-icon">🌱</span>
                <h2>我的小农场</h2>
            </div>
            
            <div class="farm-grid plots-container">
                <div class="plot-card plot" id="cute-plot-0">
                    <div class="plot-header">
                        <div class="plot-checkbox-container">
                            <input type="checkbox" class="plot-checkbox" data-plot-id="0">
                            <span class="plot-number">1</span>
                        </div>
                        <button class="plot-action clickable-item" data-plot-id="0">🌰 种植</button>
                    </div>
                    <div class="plot-visual">
                        <div class="soil"></div>
                        <div class="plant plot-icon"></div>
                    </div>
                    <div class="plot-info">
                        <div class="plot-name-display">
                            <span class="plot-name">空地</span>
                        </div>
                        <div class="plot-stats">
                            <div class="stat">
                                <span class="stat-icon">💧</span>
                                <span class="stat-value plot-moisture">50%</span>
                            </div>
                            <div class="stat">
                                <span class="stat-icon">🌿</span>
                                <span class="stat-value plot-fertility">50%</span>
                            </div>
                        </div>
                        <div class="plot-status">
                            <div class="plot-stage-display">
                                生长: <span class="plot-stage">-</span>
                            </div>
                            <div class="plot-timer-display">
                                计时: <span class="plot-timer">-</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="plot-card plot" id="cute-plot-1">
                    <div class="plot-header">
                        <div class="plot-checkbox-container">
                            <input type="checkbox" class="plot-checkbox" data-plot-id="1">
                            <span class="plot-number">2</span>
                        </div>
                        <button class="plot-action clickable-item" data-plot-id="1">🌰 种植</button>
                    </div>
                    <div class="plot-visual">
                        <div class="soil"></div>
                        <div class="plant plot-icon"></div>
                    </div>
                    <div class="plot-info">
                        <div class="plot-name-display">
                            <span class="plot-name">空地</span>
                        </div>
                        <div class="plot-stats">
                            <div class="stat">
                                <span class="stat-icon">💧</span>
                                <span class="stat-value plot-moisture">50%</span>
                            </div>
                            <div class="stat">
                                <span class="stat-icon">🌿</span>
                                <span class="stat-value plot-fertility">50%</span>
                            </div>
                        </div>
                        <div class="plot-status">
                            <div class="plot-stage-display">
                                生长: <span class="plot-stage">-</span>
                            </div>
                            <div class="plot-timer-display">
                                计时: <span class="plot-timer">-</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="plot-card plot" id="cute-plot-2">
                    <div class="plot-header">
                        <div class="plot-checkbox-container">
                            <input type="checkbox" class="plot-checkbox" data-plot-id="2">
                            <span class="plot-number">3</span>
                        </div>
                        <button class="plot-action clickable-item" data-plot-id="2">🌰 种植</button>
                    </div>
                    <div class="plot-visual">
                        <div class="soil"></div>
                        <div class="plant plot-icon"></div>
                    </div>
                    <div class="plot-info">
                        <div class="plot-name-display">
                            <span class="plot-name">空地</span>
                        </div>
                        <div class="plot-stats">
                            <div class="stat">
                                <span class="stat-icon">💧</span>
                                <span class="stat-value plot-moisture">50%</span>
                            </div>
                            <div class="stat">
                                <span class="stat-icon">🌿</span>
                                <span class="stat-value plot-fertility">50%</span>
                            </div>
                        </div>
                        <div class="plot-status">
                            <div class="plot-stage-display">
                                生长: <span class="plot-stage">-</span>
                            </div>
                            <div class="plot-timer-display">
                                计时: <span class="plot-timer">-</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="plot-card plot" id="cute-plot-3">
                    <div class="plot-header">
                        <div class="plot-checkbox-container">
                            <input type="checkbox" class="plot-checkbox" data-plot-id="3">
                            <span class="plot-number">4</span>
                        </div>
                        <button class="plot-action clickable-item" data-plot-id="3">🌰 种植</button>
                    </div>
                    <div class="plot-visual">
                        <div class="soil"></div>
                        <div class="plant plot-icon"></div>
                    </div>
                    <div class="plot-info">
                        <div class="plot-name-display">
                            <span class="plot-name">空地</span>
                        </div>
                        <div class="plot-stats">
                            <div class="stat">
                                <span class="stat-icon">💧</span>
                                <span class="stat-value plot-moisture">50%</span>
                            </div>
                            <div class="stat">
                                <span class="stat-icon">🌿</span>
                                <span class="stat-value plot-fertility">50%</span>
                            </div>
                        </div>
                        <div class="plot-status">
                            <div class="plot-stage-display">
                                生长: <span class="plot-stage">-</span>
                            </div>
                            <div class="plot-timer-display">
                                计时: <span class="plot-timer">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 🍳 厨房页面 -->
        <section class="tab-content game-content" id="kitchen-tab">
            <div class="section-title">
                <span class="title-icon">🍳</span>
                <h2>温馨小厨房</h2>
            </div>

            <!-- 炉灶区域 -->
            <div class="kitchen-area stoves-container">
                <h3 class="area-title">🔥 炉灶制茶</h3>
                <div class="stoves-grid stoves">
                    <div class="stove-card stove" id="cute-stove-1">
                        <div class="stove-visual">
                            <div class="fire">🔥</div>
                            <div class="pot">🫖</div>
                            <div class="steam">💨</div>
                        </div>
                        <div class="stove-info">
                            <div class="stove-label">炉灶1</div>
                            <div class="stove-state">点击放水</div>
                        </div>
                    </div>

                    <div class="stove-card stove" id="cute-stove-2">
                        <div class="stove-visual">
                            <div class="pot">🫖</div>
                        </div>
                        <div class="stove-info">
                            <div class="stove-label">炉灶2</div>
                            <div class="stove-state">点击放水</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 案板区域 -->
            <div class="kitchen-area processing-container">
                <h3 class="area-title">🔪 案板加工</h3>
                <div class="processing-board" id="cute-processing-board">
                    <div class="board-visual">
                        <div class="cutting-board">📋</div>
                        <div class="ingredients">🥕🌿</div>
                    </div>
                    <div class="processing-label">案板</div>
                    <div class="processing-state">点击加工材料</div>
                </div>
                <div class="processing-recipes">
                    <button class="recipe-chip recipe-btn" data-recipe="红糖" data-output="3">甘蔗→红糖x3</button>
                    <button class="recipe-chip recipe-btn" data-recipe="薄荷叶" data-output="3">薄荷→薄荷叶x3</button>
                    <button class="recipe-chip recipe-btn" data-recipe="姜丝" data-output="3">生姜→姜丝x3</button>
                    <button class="recipe-chip recipe-btn" data-recipe="柚子丝" data-output="3">柚子→柚子丝x3</button>
                    <button class="recipe-chip recipe-btn" data-recipe="银耳丝" data-output="3">银耳→银耳丝x3</button>
                    <button class="recipe-chip recipe-btn" data-recipe="柠檬片" data-output="3">柠檬→柠檬片x3</button>
                    <!-- 新增加工配方 -->
                    <button class="recipe-chip recipe-btn" data-recipe="水蜜桃果肉" data-output="3">水蜜桃→果肉x3</button>
                    <button class="recipe-chip recipe-btn" data-recipe="黄芪片" data-output="3">黄芪→黄芪片x3</button>
                    <button class="recipe-chip recipe-btn" data-recipe="干桂花" data-output="3">桂花→干桂花x3</button>
                    <button class="recipe-chip recipe-btn" data-recipe="小圆子" data-output="3">糯米→小圆子x3</button>
                    <button class="recipe-chip recipe-btn" data-recipe="酒酿" data-output="3">米→酒酿x3</button>
                </div>
            </div>
        </section>

        <!-- 🍵 茶摊页面 -->
        <section class="tab-content game-content" id="tea-tab">
            <div class="section-title">
                <span class="title-icon">🍵</span>
                <h2>温馨茶摊</h2>
            </div>

            <!-- 制作好的茶饮 -->
            <div class="tea-display tea-container">
                <div class="no-tea">还没有制作好的茶饮</div>
            </div>

            <!-- 小料区域 -->
            <div class="toppings-area">
                <h3 class="area-title">🍯 可用小料</h3>
                <div class="toppings-grid toppings-display" id="cute-toppings-display">
                    <!-- 小料将在这里动态生成 -->
                </div>
            </div>
        </section>

        <!-- 🛒 浮动购物车按钮 -->
        <button class="floating-cart" id="cute-floating-cart-btn">
            <span class="cart-icon">🛒</span>
            <span class="cart-badge" id="cute-cart-badge">0</span>
        </button>

        <!-- 可爱版暂停遮罩 -->
        <div class="pause-overlay" id="cute-pause-overlay">
            <div>🍵 游戏已暂停</div>
            <button class="resume-btn" id="cute-resume-btn">继续游戏</button>
        </div>

        <!-- 隐藏的原有游戏元素 - 保持原有ID以供游戏逻辑使用 -->
        <div style="display: none;">
            <!-- 原有顶部控制区 -->
            <header>
                <div class="top-bar">
                    <div class="coins-display">
                        <span>铜板: </span>
                        <span id="coins-count">100</span>
                    </div>
                    <div class="title">Tea Shop</div>
                    <div class="menu-btn" id="menu-btn">≡</div>
                </div>

                <!-- 菜单面板 -->
                <div class="menu-panel" id="menu-panel">
                    <button id="save-manager">存档管理</button>
                    <button id="recipe-button">查看配方</button>
                    <button id="collection-button">集卡册</button>
                    <button id="unlock-progress-button">解锁进度</button>
                    <button id="special-customer-test-button">特殊顾客解锁测试</button>
                    <button id="pause-btn" class="menu-pause-btn">暂停游戏</button>
                    <button id="close-menu">关闭</button>
                </div>
            </header>

            <!-- 保持原有的信息面板结构以供JS使用 -->
            <div class="info-swiper">
                <div class="swiper-wrapper">
                    <div class="swiper-slide weather-season">
                        <div class="info-block">
                            <div class="game-status">
                                <div class="status-info">
                                    <span class="season-badge" id="season">春天</span>
                                    <span class="weather-badge" id="weather">晴天</span>
                                    <span class="day-badge">第<span id="day">1</span>天</span>
                                </div>
                                <div class="status-buttons">
                                    <button id="basket-btn" class="status-btn basket-btn" title="查看篮子">🧺</button>
                                    <button id="buy-seed-status" class="status-btn shop-btn" title="打开商店">🏪</button>
                                </div>
                            </div>
                            <div class="customer-status">
                                <div class="customer-info-row">
                                    <span class="info-label">顾客:</span>
                                    <span id="customer-name">暂无顾客</span>
                                    <span class="info-label">耐心:</span>
                                    <span id="patience-timer">-</span>秒
                                </div>
                                <div class="customer-info-row">
                                    <span class="info-label">想要:</span>
                                    <span id="customer-tea">-</span>
                                    <span class="info-label">加料:</span>
                                    <span id="customer-toppings">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="swiper-slide message-log">
                        <div class="message-content">
                            <div class="message">欢迎来到古风茶铺!</div>
                        </div>
                    </div>
                </div>
                <div class="swiper-pagination"></div>
            </div>

            <!-- 原有田地结构 -->
            <div class="plots-container">
                <div class="plot" id="plot-0">
                    <div class="plot-name">空地</div>
                    <div class="plot-moisture">50%</div>
                    <div class="plot-fertility">50%</div>
                    <div class="plot-stage">-</div>
                    <div class="plot-timer">-</div>
                </div>
                <div class="plot" id="plot-1">
                    <div class="plot-name">空地</div>
                    <div class="plot-moisture">50%</div>
                    <div class="plot-fertility">50%</div>
                    <div class="plot-stage">-</div>
                    <div class="plot-timer">-</div>
                </div>
                <div class="plot" id="plot-2">
                    <div class="plot-name">空地</div>
                    <div class="plot-moisture">50%</div>
                    <div class="plot-fertility">50%</div>
                    <div class="plot-stage">-</div>
                    <div class="plot-timer">-</div>
                </div>
                <div class="plot" id="plot-3">
                    <div class="plot-name">空地</div>
                    <div class="plot-moisture">50%</div>
                    <div class="plot-fertility">50%</div>
                    <div class="plot-stage">-</div>
                    <div class="plot-timer">-</div>
                </div>
            </div>

            <!-- 原有炉灶结构 -->
            <div class="stoves">
                <div class="stove" id="stove-1">
                    <div class="stove-state">点击放水</div>
                </div>
                <div class="stove" id="stove-2">
                    <div class="stove-state">点击放水</div>
                </div>
            </div>

            <!-- 原有小料显示 -->
            <div id="toppings-display"></div>

            <!-- 原有案板 -->
            <div id="processing-board"></div>

            <!-- 原有茶饮容器 -->
            <div class="tea-container" id="tea-container">
                <!-- 茶饮将在这里动态生成 -->
            </div>

            <!-- 原有购物车相关 -->
            <div id="cart-items-count">0</div>
            
            <!-- 原有商店相关 -->
            <div class="coins-display shop-coins">
                <span>铜板: </span>
                <span id="shop-coins-count">100</span>
            </div>

            <!-- 商店遮罩层 -->
            <div id="shop-overlay" class="shop-overlay"></div>
        
            <!-- 种子/商店面板 -->
            <div class="shop-panel" id="seed-panel">
                <div class="shop-header">
                    <div id="selected-shop-item">请选择商品</div>
                    <div class="coins-display shop-coins">
                        <span>铜板: </span>
                        <span id="shop-coins-count">100</span>
                    </div>
                    <button id="close-shop" class="close-btn">×</button>
                </div>

                <div class="shop-content">
                    <div class="shop-items-container">
                        <!-- 种子区域 -->
                        <div class="shop-section">
                            <div class="shop-section-title">种子</div>
                            <div class="seed-grid">
                                <button class="seed-btn" data-price="1">五味子</button>
                                <button class="seed-btn" data-price="1">乌梅</button>
                                <button class="seed-btn" data-price="1">山楂</button>
                                <button class="seed-btn" data-price="1">陈皮</button>
                                <button class="seed-btn" data-price="1">甘草</button>
                                <button class="seed-btn" data-price="1">桂花</button>
                                <button class="seed-btn" data-price="1">大麦</button>
                                <button class="seed-btn" data-price="1">菊花</button>
                                <button class="seed-btn" data-price="1">金银花</button>
                                <button class="seed-btn" data-price="1">决明子</button>
                                <button class="seed-btn" data-price="1">枸杞</button>
                                <button class="seed-btn" data-price="1">生姜</button>
                                <button class="seed-btn" data-price="1">桂圆</button>
                                <button class="seed-btn" data-price="1">红枣</button>
                                <button class="seed-btn" data-price="1">薄荷</button>
                                <button class="seed-btn" data-price="1">玫瑰花</button>
                                <button class="seed-btn" data-price="1">洛神花</button>
                                <button class="seed-btn" data-price="1">冬瓜</button>
                                <button class="seed-btn" data-price="1">荷叶</button>
                                <button class="seed-btn" data-price="1">薏米</button>
                                <button class="seed-btn" data-price="1">雪花梨</button>
                                <button class="seed-btn" data-price="1">甘蔗</button>
                                <button class="seed-btn" data-price="1">柚子</button>
                                <button class="seed-btn" data-price="1">话梅</button>
                                <button class="seed-btn" data-price="1">柠檬</button>
                                <!-- 新增种子 -->
                                <button class="seed-btn" data-price="2">桑叶</button>
                                <button class="seed-btn" data-price="2">杭白菊</button>
                                <button class="seed-btn" data-price="3">水蜜桃</button>
                                <button class="seed-btn" data-price="3">黄芪</button>
                                <button class="seed-btn" data-price="2">白茅根</button>
                                <button class="seed-btn" data-price="2">马蹄</button>
                                <button class="seed-btn" data-price="2">糯米</button>
                                <button class="seed-btn" data-price="1">米</button>
                            </div>
                        </div>

                        <!-- 物品区域 -->
                        <div class="shop-section">
                            <div class="shop-section-title">物品</div>
                            <div class="item-grid">
                                <button class="shop-item-btn" data-item="蜂蜜" data-price="3">蜂蜜 (3铜板)</button>
                                <button class="shop-item-btn" data-item="银耳" data-price="3">银耳 (3铜板)</button>
                                <button class="shop-item-btn" data-item="红糖" data-price="2">红糖 (2铜板)</button>
                                <button class="shop-item-btn" data-item="薄荷叶" data-price="2">薄荷叶 (2铜板)</button>
                                <!-- 新增物品 -->
                                <button class="shop-item-btn" data-item="冰糖" data-price="3">冰糖 (3铜板)</button>
                                <button class="shop-item-btn" data-item="乌龙茶包" data-price="4">乌龙茶包 (4铜板)</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 新的购物车预览 - 独立组件 -->
                    <div class="cart-preview">
                        <div class="cart-preview-header">
                            <div class="cart-preview-title">购物车</div>
                            <div class="cart-preview-total">
                                <div><span id="cart-items-count">0</span> 件商品</div>
                                <div>总计: <span id="cart-total">0</span> 铜板</div>
                            </div>
                        </div>
                        
                        <div class="cart-preview-items">
                            <div class="cart-preview-empty">购物车是空的</div>
                        </div>
                        
                        <button class="cart-preview-button" id="checkout-btn">查看购物车</button>
                    </div>
                </div>
            </div>

            <!-- 添加到购物车提示 -->
            <div class="add-to-cart-hint" id="add-to-cart-hint" style="display: none;">
                已添加到购物车
            </div>

            <!-- 小篮子选择窗口 -->
            <div class="basket-select-panel" id="basket-select-panel">
                <div class="basket-select-header">
                    <div class="basket-select-title">选择种植物品</div>
                    <button id="confirm-plant-btn" class="confirm-plant-top-btn" disabled>确认种植</button>
                    <button id="go-to-shop-btn" class="go-to-shop-btn" style="display: none;">去购买</button>
                    <button id="close-basket-select" class="close-btn">×</button>
                </div>
                <div class="basket-select-content">
                    <div class="basket-select-info">
                        <div class="selected-plot-info">
                            <span>田地: </span><span id="selected-plot-name">1号田地</span>
                        </div>
                    </div>

                    <div class="seed-selection-section">
                        <div class="seed-section-title">库存种子：</div>
                        <div class="seed-grid available-seeds" id="available-seeds-grid">
                            <!-- 有库存的种子将在这里动态生成 -->
                        </div>
                    </div>

                    <div class="seed-selection-section">
                        <div class="seed-section-title">缺货种子（点击购买）：</div>
                        <div class="seed-grid unavailable-seeds" id="unavailable-seeds-grid">
                            <!-- 没有库存的种子将在这里动态生成 -->
                        </div>
                    </div>

                    <!-- 购物车显示区域 -->
                    <div class="basket-cart-section" id="basket-cart-section" style="display: none;">
                        <div class="basket-cart-title">🛒 购物车内容</div>
                        <div class="basket-cart-items" id="basket-cart-items">
                            <!-- 购物车物品将在这里动态生成 -->
                        </div>
                        <div class="basket-cart-total" id="basket-cart-total">
                            总计: 0 铜板
                        </div>
                    </div>
                </div>
            </div>

            <!-- 配方面板 -->
            <div class="recipe-panel" id="recipe-panel">
                <div class="recipe-select-header">
                    <div class="recipe-select-title">茶饮配方</div>
                    <button id="close-recipe" class="close-btn">×</button>
                </div>
                <div class="recipe-items">
                    <div class="recipe-item">
                        <div class="recipe-name">五味子饮</div>
                        <div class="recipe-ingredients">五味子</div>
                    </div>
                    <div class="recipe-item">
                        <div class="recipe-name">古法酸梅汤</div>
                        <div class="recipe-ingredients">乌梅, 山楂, 陈皮, 甘草, 桂花</div>
                    </div>
                    <div class="recipe-item">
                        <div class="recipe-name">焦香大麦茶</div>
                        <div class="recipe-ingredients">大麦</div>
                    </div>
                    <div class="recipe-item">
                        <div class="recipe-name">三花决明茶</div>
                        <div class="recipe-ingredients">菊花, 金银花, 决明子, 枸杞</div>
                    </div>
                    <div class="recipe-item">
                        <div class="recipe-name">陈皮姜米茶</div>
                        <div class="recipe-ingredients">陈皮, 生姜</div>
                    </div>
                    <div class="recipe-item">
                        <div class="recipe-name">桂圆红枣茶</div>
                        <div class="recipe-ingredients">桂圆, 红枣, 枸杞</div>
                    </div>
                    <div class="recipe-item">
                        <div class="recipe-name">薄荷甘草凉茶</div>
                        <div class="recipe-ingredients">薄荷, 甘草</div>
                    </div>
                    <div class="recipe-item">
                        <div class="recipe-name">洛神玫瑰饮</div>
                        <div class="recipe-ingredients">洛神花, 玫瑰花, 山楂</div>
                    </div>
                    <div class="recipe-item">
                        <div class="recipe-name">冬瓜荷叶饮</div>
                        <div class="recipe-ingredients">冬瓜, 荷叶, 薏米</div>
                    </div>
                    <div class="recipe-item">
                        <div class="recipe-name">小吊梨汤</div>
                        <div class="recipe-ingredients">雪花梨, 银耳丝, 话梅, 枸杞</div>
                    </div>
                    <div class="recipe-item">
                        <div class="recipe-name">柠檬茶</div>
                        <div class="recipe-ingredients">柠檬</div>
                    </div>
                    
                    <!-- 按顾客解锁的配方 -->
                    <div class="recipe-item special-unlock" data-customer="凌小路" style="display: none;">
                        <div class="recipe-name">🔒 洛神玫瑰饮</div>
                        <div class="recipe-ingredients">需要凌小路来访解锁</div>
                    </div>
                    <div class="recipe-item special-unlock" data-customer="花花" style="display: none;">
                        <div class="recipe-name">🔒 桂圆红枣茶</div>
                        <div class="recipe-ingredients">需要花花来访解锁</div>
                    </div>
                    <div class="recipe-item special-unlock" data-customer="江飞飞" style="display: none;">
                        <div class="recipe-name">🔒 焦香大麦茶</div>
                        <div class="recipe-ingredients">需要江飞飞来访解锁</div>
                    </div>
                    <div class="recipe-item special-unlock" data-customer="江三" style="display: none;">
                        <div class="recipe-name">🔒 三花决明茶</div>
                        <div class="recipe-ingredients">需要江三来访解锁</div>
                    </div>
                    <div class="recipe-item special-unlock" data-customer="江四" style="display: none;">
                        <div class="recipe-name">🔒 薄荷甘草凉茶</div>
                        <div class="recipe-ingredients">需要江四来访解锁</div>
                    </div>
                    <div class="recipe-item special-unlock" data-customer="池云旗" style="display: none;">
                        <div class="recipe-name">🔒 陈皮姜米茶</div>
                        <div class="recipe-ingredients">需要池云旗来访解锁</div>
                    </div>
                    <div class="recipe-item special-unlock" data-customer="江潮" style="display: none;">
                        <div class="recipe-name">🔒 冬瓜荷叶饮</div>
                        <div class="recipe-ingredients">需要江潮来访解锁</div>
                    </div>
                    <div class="recipe-item special-unlock" data-customer="池惊暮" style="display: none;">
                        <div class="recipe-name">🔒 古法酸梅汤</div>
                        <div class="recipe-ingredients">需要池惊暮来访解锁</div>
                    </div>
                    <div class="recipe-item special-unlock" data-customer="江敕封" style="display: none;">
                        <div class="recipe-name">🔒 小吊梨汤</div>
                        <div class="recipe-ingredients">需要江敕封来访解锁</div>
                    </div>
                    
                    <!-- 按人数解锁的配方 -->
                    <div class="recipe-item unlockable-recipe" data-unlock="30" style="display: none;">
                        <div class="recipe-name">🔒 桑菊润燥茶</div>
                        <div class="recipe-ingredients">服务30位顾客后解锁</div>
                    </div>
                    <div class="recipe-item unlockable-recipe" data-unlock="60" style="display: none;">
                        <div class="recipe-name">🔒 桂花酒酿饮</div>
                        <div class="recipe-ingredients">服务60位顾客后解锁</div>
                    </div>
                    <div class="recipe-item unlockable-recipe" data-unlock="90" style="display: none;">
                        <div class="recipe-name">🔒 蜜桃乌龙冷萃</div>
                        <div class="recipe-ingredients">服务90位顾客后解锁</div>
                    </div>
                    <div class="recipe-item unlockable-recipe" data-unlock="120" style="display: none;">
                        <div class="recipe-name">🔒 黄芪枸杞茶</div>
                        <div class="recipe-ingredients">服务120位顾客后解锁</div>
                    </div>
                    <div class="recipe-item unlockable-recipe" data-unlock="150" style="display: none;">
                        <div class="recipe-name">🔒 竹蔗茅根马蹄水</div>
                        <div class="recipe-ingredients">服务150位顾客后解锁</div>
                    </div>
                </div>
            </div>

            <!-- 炉灶配方选择面板 -->
            <div class="recipe-select-panel" id="recipe-select-panel">
                <div class="recipe-select-header">
                    <div class="recipe-select-title">选择茶饮配方</div>
                    <button id="close-recipe-select" class="close-btn">×</button>
                </div>
                <div class="recipe-select-content">
                    <div class="recipe-select-list">
                        <!-- 配方列表将在JS中动态生成 -->
                    </div>
                    <div class="recipe-select-info">
                        <div class="recipe-select-description">
                            <div id="selected-recipe-name">请选择茶饮</div>
                            <div id="selected-recipe-ingredients">需要的材料将显示在这里</div>
                        </div>
                        <div class="recipe-select-buttons">
                            <button id="make-recipe-btn" disabled>制作茶饮</button>
                            <button id="cancel-recipe-btn">取消</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 解锁进度面板 -->
            <div class="unlock-progress-panel" id="unlock-progress-panel" style="display: none;">
                <div class="unlock-header">
                    <div class="unlock-title">配方解锁进度</div>
                    <button id="close-unlock-progress" class="close-btn">×</button>
                </div>
                <div class="unlock-content">
                    <div class="progress-info">
                        <div class="current-progress">
                            已服务顾客: <span id="served-customers-count">0</span>位
                        </div>
                        <div class="next-unlock">
                            距离下一个解锁: <span id="next-unlock-info">查看下方列表</span>
                        </div>
                    </div>

                    <div class="unlock-recipe-list">
                        <div class="unlock-section-title">配方解锁条件</div>
                        <div class="unlock-recipes" id="unlock-recipes-list">
                            <!-- 解锁配方列表将在JavaScript中动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 篮子查看面板 -->
            <div class="basket-view-panel" id="basket-view-panel" style="display: none;">
                <div class="basket-view-header">
                    <div class="basket-view-title">🧺 我的篮子</div>
                    <button id="close-basket-view" class="close-btn">×</button>
                </div>
                <div class="basket-view-content">
                    <div class="basket-view-info">
                        <div class="basket-section">
                            <div class="basket-section-title">🌱 种子库存</div>
                            <div class="basket-items" id="basket-seeds-display">
                                <!-- 种子将在这里动态生成 -->
                            </div>
                        </div>

                        <div class="basket-section">
                            <div class="basket-section-title">🌿 收获物品</div>
                            <div class="basket-items" id="basket-materials-display">
                                <!-- 收获物品将在这里动态生成 -->
                            </div>
                        </div>

                        <div class="basket-section">
                            <div class="basket-section-title">🧂 小料库存</div>
                            <div class="basket-items" id="basket-toppings-display">
                                <!-- 小料将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 购物车弹出窗口 -->
            <div class="cart-popup-panel" id="cart-popup-panel" style="display: none;">
                <div class="cart-popup-header">
                    <div class="cart-popup-title">🛒 购物车</div>
                    <button id="close-cart-popup" class="close-btn">×</button>
                </div>
                <div class="cart-popup-content">
                    <div class="cart-popup-summary">
                        <div class="cart-popup-count">商品数量: <span id="cart-popup-items-count">0</span> 个</div>
                        <div class="cart-popup-total">总计: <span id="cart-popup-total">0</span> 铜板</div>
                    </div>
                    <div class="cart-popup-items" id="cart-popup-items">
                        <div class="cart-empty-message">购物车是空的</div>
                    </div>
                    <div class="cart-popup-actions">
                        <button class="cart-popup-btn clear-btn" id="cart-popup-clear">清空购物车</button>
                        <button class="cart-popup-btn checkout-btn" id="cart-popup-checkout">去结账</button>
                    </div>
                </div>
            </div>

            <!-- 测试模式面板 -->
            <div class="test-mode-panel" id="test-mode-panel" style="display: none;">
                <div class="test-header">
                    <div class="test-title">🍵 茶铺测试中心</div>
                    <button id="close-test-mode" class="close-btn">×</button>
                </div>
                <div class="test-content">
                    <!-- 快速测试区 -->
                    <div class="test-section">
                        <div class="test-section-title">⚡ 快速测试</div>
                        <div class="test-controls">
                            <button id="spawn-customer-test" class="test-btn">生成顾客</button>
                            <button id="add-tea-test" class="test-btn">制作所有茶饮</button>
                            <button id="add-toppings-test" class="test-btn">添加所有小料</button>
                            <button id="unlock-all-test" class="test-btn warning">解锁所有配方</button>
                        </div>
                    </div>

                    <!-- 特殊顾客测试区 -->
                    <div class="test-section">
                        <div class="test-section-title">👑 特殊顾客测试</div>
                        <div class="test-controls special-customers">
                            <button class="test-btn special" data-customer="凌小路" data-recipe="洛神玫瑰饮">凌小路→洛神玫瑰饮</button>
                            <button class="test-btn special" data-customer="花花" data-recipe="桂圆红枣茶">花花→桂圆红枣茶</button>
                            <button class="test-btn special" data-customer="江飞飞" data-recipe="焦香大麦茶">江飞飞→焦香大麦茶</button>
                            <button class="test-btn special" data-customer="江三" data-recipe="三花决明茶">江三→三花决明茶</button>
                            <button class="test-btn special" data-customer="江四" data-recipe="薄荷甘草凉茶">江四→薄荷甘草凉茶</button>
                            <button class="test-btn special" data-customer="池云旗" data-recipe="陈皮姜米茶">池云旗→陈皮姜米茶</button>
                            <button class="test-btn special" data-customer="江潮" data-recipe="冬瓜荷叶饮">江潮→冬瓜荷叶饮</button>
                            <button class="test-btn special" data-customer="池惊暮" data-recipe="古法酸梅汤">池惊暮→古法酸梅汤</button>
                            <button class="test-btn special" data-customer="江敕封" data-recipe="小吊梨汤">江敕封→小吊梨汤</button>
                        </div>
                    </div>

                    <!-- 人数解锁测试区 -->
                    <div class="test-section">
                        <div class="test-section-title">📈 人数解锁测试</div>
                        <div class="test-controls">
                            <button class="test-btn customer-count-btn" data-count="30">设置30人→桑菊润燥茶</button>
                            <button class="test-btn customer-count-btn" data-count="60">设置60人→桂花酒酿饮</button>
                            <button class="test-btn customer-count-btn" data-count="90">设置90人→蜜桃乌龙冷萃</button>
                            <button class="test-btn customer-count-btn" data-count="120">设置120人→黄芪枸杞茶</button>
                            <button class="test-btn customer-count-btn" data-count="150">设置150人→竹蔗茅根马蹄水</button>
                        </div>
                    </div>

                    <!-- 系统测试区 -->
                    <div class="test-section">
                        <div class="test-section-title">🔧 系统测试</div>
                        <div class="test-controls">
                            <button class="test-btn" id="check-recipe-status-btn">检查配方状态</button>
                            <button class="test-btn" id="check-customer-visits-btn">检查顾客访问</button>
                            <button class="test-btn danger" id="reset-progress-btn">重置解锁进度</button>
                            <button id="exit-test-mode" class="test-btn danger">退出测试模式</button>
                        </div>
                    </div>

                    <!-- 测试状态 -->
                    <div class="test-status">
                        <div class="test-status-title">📊 测试状态</div>
                        <div class="test-status-content">
                            <div>状态：<span id="test-status-text">准备就绪</span></div>
                            <div>服务顾客：<span id="test-customer-count">0</span>位</div>
                            <div>解锁配方：<span id="test-recipe-count">2</span>个</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 原版暂停遮罩 -->
            <div class="pause-overlay" id="pause-overlay">
                <div>游戏已暂停</div>
                <button class="resume-btn" id="resume-btn">继续游戏</button>
            </div>
        </div>
    </div>

    <!-- 引用原有游戏逻辑 -->
    <script src="tea_shop_mobile.js"></script>
    <!-- 可爱UI交互脚本 - 全面修复版 v2.0 -->
    <script src="cute_tea_shop_enhanced_v2.js"></script>

    <!-- 引入面板 -->
    <div id="panels-container"></div>
    <script>
        // 加载面板
        async function loadPanels() {
            try {
                const response = await fetch('panels.html');
                const html = await response.text();
                document.getElementById('panels-container').innerHTML = html;
                console.log('面板加载成功');
                
                // 触发面板加载完成事件
                const event = new CustomEvent('panelsLoaded');
                window.dispatchEvent(event);
            } catch (error) {
                console.error('加载面板失败:', error);
            }
        }

        // 页面加载完成后加载面板
        window.addEventListener('DOMContentLoaded', loadPanels);
    </script>

    <script>
        // 确保按钮元素存在
        window.addEventListener('DOMContentLoaded', function() {
            const requiredButtons = [
                'cute-basket-btn',
                'cute-buy-seed-status',
                'cute-recipe-button',
                'cute-floating-cart-btn'
            ];

            requiredButtons.forEach(buttonId => {
                if (!document.getElementById(buttonId)) {
                    console.error(`Button ${buttonId} not found, creating it...`);
                    const button = document.createElement('button');
                    button.id = buttonId;
                    button.style.display = 'none';
                    document.body.appendChild(button);
                }
            });
        });
    </script>

    <!-- 游戏面板 -->
    <div class="game-panels">
        <!-- 配方面板 -->
        <div id="recipe-panel" class="panel">
            <div class="panel-header">
                <h2>📜 配方大全</h2>
                <button class="close-btn">✕</button>
            </div>
            <div class="panel-content">
                <div class="recipe-list">
                    <!-- 配方列表将动态生成 -->
                    <div class="recipe-item">
                        <div class="recipe-name">五味子饮</div>
                        <div class="recipe-ingredients">五味子</div>
                    </div>
                    <div class="recipe-item">
                        <div class="recipe-name">古法酸梅汤</div>
                        <div class="recipe-ingredients">乌梅, 山楂, 陈皮, 甘草, 桂花</div>
                    </div>
                    <!-- 更多配方会动态添加 -->
                </div>
            </div>
        </div>

        <!-- 篮子选择面板 -->
        <div id="basket-select-panel" class="panel">
            <div class="panel-header">
                <h2>🧺 选择种植物品</h2>
                <button class="close-btn">✕</button>
            </div>
            <div class="panel-content">
                <div class="basket-select-info">
                    <div class="selected-plot-info">
                        <span>田地: </span><span id="selected-plot-name">1号田地</span>
                    </div>
                </div>
                <div class="seed-selection">
                    <div class="seed-section">
                        <h3>库存种子：</h3>
                        <div id="available-seeds-grid" class="seed-grid"></div>
                    </div>
                    <div class="seed-section">
                        <h3>缺货种子（点击购买）：</h3>
                        <div id="unavailable-seeds-grid" class="seed-grid"></div>
                    </div>
                </div>
                <div class="basket-actions">
                    <button id="confirm-plant-btn" disabled>确认种植</button>
                    <button id="go-to-shop-btn" style="display: none;">去购买</button>
                </div>
            </div>
        </div>

        <!-- 商店面板 -->
        <div id="seed-panel" class="panel">
            <div class="panel-header">
                <h2>🏪 茶叶商店</h2>
                <div class="shop-coins">
                    <span>🪙</span>
                    <span id="shop-coins-count">100</span>
                </div>
                <button class="close-btn">✕</button>
            </div>
            <div class="panel-content">
                <div class="shop-sections">
                    <div class="shop-section">
                        <h3>种子</h3>
                        <div class="seed-grid">
                            <!-- 种子列表将动态生成 -->
                        </div>
                    </div>
                    <div class="shop-section">
                        <h3>物品</h3>
                        <div class="item-grid">
                            <!-- 物品列表将动态生成 -->
                        </div>
                    </div>
                </div>
                <div class="cart-preview">
                    <div class="cart-header">
                        <h3>购物车</h3>
                        <div class="cart-total">
                            总计: <span id="cart-total">0</span> 铜板
                        </div>
                    </div>
                    <div id="cart-items" class="cart-items"></div>
                    <button id="checkout-btn" class="checkout-btn">结算</button>
                </div>
            </div>
        </div>

        <!-- 购物车面板 -->
        <div id="cart-popup-panel" class="panel">
            <div class="panel-header">
                <h2>🛒 购物车</h2>
                <button class="close-btn">✕</button>
            </div>
            <div class="panel-content">
                <div class="cart-items" id="cart-popup-items">
                    <!-- 购物车内容将动态生成 -->
                </div>
                <div class="cart-summary">
                    <div class="cart-total">
                        总计: <span id="cart-popup-total">0</span> 铜板
                    </div>
                    <div class="cart-actions">
                        <button id="cart-popup-clear">清空</button>
                        <button id="cart-popup-checkout">结算</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 解锁进度面板 -->
        <div id="unlock-progress-panel" class="panel">
            <div class="panel-header">
                <h2>🎯 解锁进度</h2>
                <button class="close-btn">✕</button>
            </div>
            <div class="panel-content">
                <div class="progress-info">
                    <div class="served-customers">
                        已服务顾客: <span id="served-customers-count">0</span>位
                    </div>
                    <div class="next-unlock">
                        距离下一个解锁: <span id="next-unlock-info">查看下方列表</span>
                    </div>
                </div>
                <div class="unlock-list" id="unlock-recipes-list">
                    <!-- 解锁列表将动态生成 -->
                </div>
            </div>
        </div>

        <!-- 测试模式面板 -->
        <div id="test-mode-panel" class="panel">
            <div class="panel-header">
                <h2>🧪 测试模式</h2>
                <button class="close-btn">✕</button>
            </div>
            <div class="panel-content">
                <div class="test-sections">
                    <div class="test-section">
                        <h3>⚡ 快速测试</h3>
                        <div class="test-buttons">
                            <button id="spawn-customer-test">生成顾客</button>
                            <button id="add-tea-test">制作所有茶饮</button>
                            <button id="add-toppings-test">添加所有小料</button>
                        </div>
                    </div>
                    <div class="test-section">
                        <h3>👑 特殊顾客测试</h3>
                        <div class="test-buttons special-customers">
                            <!-- 特殊顾客按钮将动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 遮罩层 -->
    <div class="panel-overlay"></div>

    <!-- 面板样式 -->
    <style>
        /* 面板基础样式 */
        .panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            z-index: 1000;
            max-width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            min-width: 300px;
            display: none;
            animation: panelFadeIn 0.3s ease;
        }

        .panel.show {
            display: block;
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            background: white;
            border-radius: 20px 20px 0 0;
            position: sticky;
            top: 0;
            z-index: 1;
        }

        .panel-header h2 {
            margin: 0;
            font-size: 1.2em;
            color: #FF69B4;
        }

        .panel-content {
            padding: 20px;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5em;
            color: #666;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s;
        }

        .close-btn:hover {
            background: #f0f0f0;
        }

        /* 遮罩层样式 */
        .panel-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
            animation: overlayFadeIn 0.3s ease;
        }

        .panel-overlay.show {
            display: block;
        }

        /* 动画效果 */
        @keyframes panelFadeIn {
            from {
                opacity: 0;
                transform: translate(-50%, -48%);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%);
            }
        }

        @keyframes overlayFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* 面板内容样式 */
        .recipe-list, .seed-grid, .item-grid {
            display: grid;
            gap: 15px;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            margin-top: 15px;
        }

        .recipe-item, .seed-item, .shop-item {
            background: #fff;
            border: 1px solid #f0f0f0;
            border-radius: 10px;
            padding: 15px;
            transition: all 0.3s;
        }

        .recipe-item:hover, .seed-item:hover, .shop-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        /* 购物车样式 */
        .cart-preview {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #f0f0f0;
        }

        .cart-items {
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }

        .cart-total {
            font-weight: bold;
            color: #FF69B4;
            margin: 10px 0;
        }

        .checkout-btn {
            width: 100%;
            padding: 12px;
            background: #FF69B4;
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .checkout-btn:hover {
            background: #FF1493;
        }

        /* 测试模式样式 */
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 10px;
        }

        .test-section h3 {
            color: #FF69B4;
            margin-bottom: 10px;
        }

        .test-buttons {
            display: grid;
            gap: 10px;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .panel {
                width: 95%;
                max-height: 80vh;
            }

            .recipe-list, .seed-grid, .item-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }
    </style>

    <!-- 面板控制脚本 -->
    <script>
        // 面板控制函数
        function showPanel(panelId) {
            // 隐藏所有面板
            document.querySelectorAll('.panel').forEach(panel => {
                panel.classList.remove('show');
            });
            
            // 显示选中的面板和遮罩层
            const panel = document.getElementById(panelId);
            const overlay = document.querySelector('.panel-overlay');
            if (panel) {
                panel.classList.add('show');
                overlay.classList.add('show');
            }
        }

        function hidePanel(panelId) {
            const panel = document.getElementById(panelId);
            const overlay = document.querySelector('.panel-overlay');
            if (panel) {
                panel.classList.remove('show');
                overlay.classList.remove('show');
            }
        }

        // 初始化面板控制
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有关闭按钮添加事件监听
            document.querySelectorAll('.close-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const panel = this.closest('.panel');
                    if (panel) {
                        hidePanel(panel.id);
                    }
                });
            });

            // 添加遮罩层点击事件
            const overlay = document.querySelector('.panel-overlay');
            if (overlay) {
                overlay.addEventListener('click', function() {
                    document.querySelectorAll('.panel').forEach(panel => {
                        hidePanel(panel.id);
                    });
                });
            }

            // 绑定按钮点击事件
            const buttonBindings = {
                'cute-recipe-button': 'recipe-panel',
                'cute-basket-btn': 'basket-view-panel',
                'cute-buy-seed-status': 'seed-panel',
                'cute-floating-cart-btn': 'cart-popup-panel',
                'cute-unlock-progress-button': 'unlock-progress-panel',
                'cute-special-customer-test-button': 'test-mode-panel'
            };

            for (const [buttonId, panelId] of Object.entries(buttonBindings)) {
                const button = document.getElementById(buttonId);
                if (button) {
                    button.onclick = () => showPanel(panelId);
                }
            }
        });

        // 导出面板控制函数供其他脚本使用
        window.showPanel = showPanel;
        window.hidePanel = hidePanel;
    </script>
</body>
</html> 