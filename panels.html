<!-- 配方面板 -->
<div id="recipe-panel" class="panel" style="display: none;">
    <div class="panel-header">
        <h2>📜 配方大全</h2>
        <button class="close-btn">✕</button>
    </div>
    <div class="panel-content">
        <div class="recipe-list">
            <!-- 配方列表将动态生成 -->
            <div class="recipe-item">
                <div class="recipe-name">五味子饮</div>
                <div class="recipe-ingredients">五味子</div>
            </div>
            <div class="recipe-item">
                <div class="recipe-name">古法酸梅汤</div>
                <div class="recipe-ingredients">乌梅, 山楂, 陈皮, 甘草, 桂花</div>
            </div>
            <!-- 更多配方会动态添加 -->
        </div>
    </div>
</div>

<!-- 篮子选择面板 -->
<div id="basket-select-panel" class="panel" style="display: none;">
    <div class="panel-header">
        <h2>🧺 选择种植物品</h2>
        <button class="close-btn">✕</button>
    </div>
    <div class="panel-content">
        <div class="basket-select-info">
            <div class="selected-plot-info">
                <span>田地: </span><span id="selected-plot-name">1号田地</span>
            </div>
        </div>
        <div class="seed-selection">
            <div class="seed-section">
                <h3>库存种子：</h3>
                <div id="available-seeds-grid" class="seed-grid"></div>
            </div>
            <div class="seed-section">
                <h3>缺货种子（点击购买）：</h3>
                <div id="unavailable-seeds-grid" class="seed-grid"></div>
            </div>
        </div>
        <div class="basket-actions">
            <button id="confirm-plant-btn" disabled>确认种植</button>
            <button id="go-to-shop-btn" style="display: none;">去购买</button>
        </div>
    </div>
</div>

<!-- 篮子查看面板 -->
<div id="basket-view-panel" class="panel" style="display: none;">
    <div class="panel-header">
        <h2>🧺 我的篮子</h2>
        <button class="close-btn">✕</button>
    </div>
    <div class="panel-content">
        <div class="basket-section">
            <div class="basket-section-title">🌱 种子库存</div>
            <div class="basket-items" id="basket-seeds-display">
                <!-- 种子将在这里动态生成 -->
            </div>
        </div>
        <div class="basket-section">
            <div class="basket-section-title">🌿 收获物品</div>
            <div class="basket-items" id="basket-materials-display">
                <!-- 收获物品将在这里动态生成 -->
            </div>
        </div>
        <div class="basket-section">
            <div class="basket-section-title">🧂 小料库存</div>
            <div class="basket-items" id="basket-toppings-display">
                <!-- 小料将在这里动态生成 -->
            </div>
        </div>
    </div>
</div>

<!-- 商店面板 -->
<div id="seed-panel" class="panel" style="display: none;">
    <div class="panel-header">
        <h2>🏪 茶叶商店</h2>
        <div class="shop-coins">
            <span>🪙</span>
            <span id="shop-coins-count">100</span>
        </div>
        <button class="close-btn">✕</button>
    </div>
    <div class="panel-content">
        <div class="shop-sections">
            <div class="shop-section">
                <h3>种子</h3>
                <div class="seed-grid">
                    <!-- 种子列表将动态生成 -->
                </div>
            </div>
            <div class="shop-section">
                <h3>物品</h3>
                <div class="item-grid">
                    <!-- 物品列表将动态生成 -->
                </div>
            </div>
        </div>
        <div class="cart-preview">
            <div class="cart-header">
                <h3>购物车</h3>
                <div class="cart-total">
                    总计: <span id="cart-total">0</span> 铜板
                </div>
            </div>
            <div id="cart-items" class="cart-items"></div>
            <button id="checkout-btn" class="checkout-btn">结算</button>
        </div>
    </div>
</div>

<!-- 购物车面板 -->
<div id="cart-popup-panel" class="panel" style="display: none;">
    <div class="panel-header">
        <h2>🛒 购物车</h2>
        <button class="close-btn">✕</button>
    </div>
    <div class="panel-content">
        <div class="cart-items" id="cart-popup-items">
            <!-- 购物车内容将动态生成 -->
        </div>
        <div class="cart-summary">
            <div class="cart-total">
                总计: <span id="cart-popup-total">0</span> 铜板
            </div>
            <div class="cart-actions">
                <button id="cart-popup-clear">清空</button>
                <button id="cart-popup-checkout">结算</button>
            </div>
        </div>
    </div>
</div>

<!-- 解锁进度面板 -->
<div id="unlock-progress-panel" class="panel" style="display: none;">
    <div class="panel-header">
        <h2>🎯 解锁进度</h2>
        <button class="close-btn">✕</button>
    </div>
    <div class="panel-content">
        <div class="progress-info">
            <div class="served-customers">
                已服务顾客: <span id="served-customers-count">0</span>位
            </div>
            <div class="next-unlock">
                距离下一个解锁: <span id="next-unlock-info">查看下方列表</span>
            </div>
        </div>
        <div class="unlock-list" id="unlock-recipes-list">
            <!-- 解锁列表将动态生成 -->
        </div>
    </div>
</div>

<!-- 测试模式面板 -->
<div id="test-mode-panel" class="panel" style="display: none;">
    <div class="panel-header">
        <h2>🧪 测试模式</h2>
        <button class="close-btn">✕</button>
    </div>
    <div class="panel-content">
        <div class="test-sections">
            <div class="test-section">
                <h3>⚡ 快速测试</h3>
                <div class="test-buttons">
                    <button id="spawn-customer-test">生成顾客</button>
                    <button id="add-tea-test">制作所有茶饮</button>
                    <button id="add-toppings-test">添加所有小料</button>
                </div>
            </div>
            <div class="test-section">
                <h3>👑 特殊顾客测试</h3>
                <div class="test-buttons special-customers">
                    <!-- 特殊顾客按钮将动态生成 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 面板样式 -->
<style>
.panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
    z-index: 1000;
    max-width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    min-width: 300px;
    display: none; /* 默认隐藏 */
}

.panel.show {
    display: block;
    animation: fadeIn 0.3s ease;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    background: #fff;
    border-radius: 15px 15px 0 0;
    position: sticky;
    top: 0;
    z-index: 1;
}

.panel-header h2 {
    margin: 0;
    font-size: 1.2em;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5em;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s;
}

.close-btn:hover {
    background: #f0f0f0;
}

.panel-content {
    padding: 20px;
}

/* 特定面板样式 */
.recipe-list, .basket-list, .shop-items, .recipe-options, .progress-list, .cart-items {
    display: grid;
    gap: 15px;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
}

.selected-plot {
    margin-bottom: 15px;
    padding: 10px;
    background: #f5f5f5;
    border-radius: 8px;
    text-align: center;
}

.checkout-button {
    width: 100%;
    padding: 12px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 8px;
    margin-top: 15px;
    cursor: pointer;
    transition: background 0.3s;
}

.checkout-button:hover {
    background: #388E3C;
}

.cart-total {
    margin-top: 15px;
    padding: 10px;
    background: #f5f5f5;
    border-radius: 8px;
    text-align: right;
    font-weight: bold;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translate(-50%, -48%); }
    to { opacity: 1; transform: translate(-50%, -50%); }
}

/* 遮罩层 */
.panel-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
}

.panel-overlay.show {
    display: block;
}
</style>

<!-- 遮罩层 -->
<div class="panel-overlay"></div>

<!-- 面板内容 -->
// ... existing panels code ... 