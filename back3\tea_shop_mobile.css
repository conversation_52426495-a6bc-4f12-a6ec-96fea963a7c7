/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Microsoft YaHei", "SimSun", sans-serif;
    -webkit-tap-highlight-color: transparent;
}

body {
    background-color: #e6e6e6;
    color: #4a4a4a;
    line-height: 1.6;
    font-size: 14px;
    overflow-x: hidden;
    touch-action: manipulation;
    width: 100%;
    min-height: 100vh;
}

.container {
    width: 100%;
    height: 100%;
    margin: 0 auto;
    background-color: #f2f2f2;
    padding: 10px;
    display: flex;
    flex-direction: column;
    overflow: visible;
    min-height: 100vh;
}

/* 顶部样式 */
header {
    margin-bottom: 10px;
}

.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #e9e9e9;
    border-radius: 5px;
    position: relative;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.title {
    font-size: 14px;
    font-weight: bold;
    color: #595959;
    text-align: center;
}

.menu-btn {
    font-size: 22px;
    cursor: pointer;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #d9d9d9;
}

.coins-display {
    padding: 6px 8px;
    background-color: #e0e0e0;
    border-radius: 4px;
    font-weight: bold;
    color: #4a4a4a;
    font-size: 12px;
}

#coins-count {
    color: #808080;
}

/* 菜单面板 */
.menu-panel {
    position: absolute;
    top: 60px;
    right: 10px;
    background-color: #f2f2f2;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 10px;
    z-index: 1000;
    display: none;
    max-width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.menu-panel button {
    display: block;
    width: 100%;
    margin: 5px 0;
    padding: 8px;
    text-align: left;
    border: none;
    background-color: #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    color: #4a4a4a;
}

.serve-button {
    background-color: #808080 !important;
    color: #f2f2f2 !important;
}

/* 信息滑块区域 */
.info-swiper {
    margin-bottom: 10px;
    overflow: hidden;
}

.swiper-container {
    width: 100%;
    height: 60px;
    position: relative;
}

.swiper-wrapper {
    display: flex;
    transition: transform 0.3s ease;
    height: 100%;
}

.swiper-slide {
    flex: 0 0 100%;
    height: 100%;
    padding: 10px;
    background-color: #e9e9e9;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    overflow-y: auto;
}

/* 统一滑块背景色为浅灰色 */
.swiper-slide.weather-season,
.swiper-slide.message-log {
    background-color: #f0f0f0;
}

.slide-title {
    font-weight: bold;
    margin-bottom: 10px;
    text-align: center;
    padding-bottom: 5px;
    border-bottom: 1px solid #e0e0e0;
}

/* 分页指示器 */
.swiper-pagination {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}

.swiper-pagination-bullet {
    width: 8px;
    height: 8px;
    margin: 0 5px;
    border-radius: 50%;
    background-color: #ccc;
    display: inline-block;
}

.swiper-pagination-bullet-active {
    background-color: #4CAF50;
}

/* 表格样式 */
table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

td {
    padding: 5px;
    border-bottom: 1px solid #f0f0f0;
}

td:first-child {
    font-weight: bold;
    width: 60px;
}

/* 可点击物品样式 */
.clickable-item {
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.clickable-item:hover {
    background-color: #e0e0e0;
}

.clickable-item:active {
    background-color: #d0d0d0;
}

/* 消息区域 */
.message-content {
    height: 100px;
    overflow-y: auto;
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.message-content::-webkit-scrollbar {
    display: none;
}

.message {
    padding: 4px;
    margin-bottom: 4px;
    background-color: #e0e0e0;
    border-radius: 3px;
    font-size: 12px;
}

.message.error {
    background-color: #d9d9d9;
    color: #808080;
}

/* 种子信息 */
.seed-info {
    text-align: center;
    margin: 10px 0;
    padding: 8px;
    background-color: #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
}

/* 商店面板 */
.shop-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #f2f2f2;
    z-index: 1000;
    display: none;
    flex-direction: column;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.shop-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: #808080;
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}



/* 商店选项卡 */
.shop-tabs, .recipe-tabs, .game-tabs {
    display: flex;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.tab-btn, .game-tab {
    flex: 1;
    background: none;
    border: none;
    padding: 8px;
    text-align: center;
    color: #666;
    position: relative;
    font-size: 14px;
}

.tab-btn.active, .game-tab.active {
    color: #4CAF50;
    font-weight: bold;
}

.tab-btn.active::after, .game-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #4CAF50;
}

.tab-content, .game-content {
    display: none;
    flex: 1;
    overflow-y: auto;
}

.tab-content.active, .game-content.active {
    display: block;
}

/* 种子网格 */
.seed-grid, .item-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    padding: 10px 0;
}

.seed-btn, .shop-item-btn {
    padding: 8px 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #e0e0e0;
    color: #4a4a4a;
    font-size: 12px;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.seed-btn:active, .shop-item-btn:active {
    transform: scale(0.95);
    background-color: #e0e0e0;
}

/* 点击波纹效果 */
.seed-btn::after, .shop-item-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, .5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.seed-btn:active::after, .shop-item-btn:active::after {
    animation: ripple 0.4s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    20% {
        transform: scale(25, 25);
        opacity: 0.3;
    }
    100% {
        opacity: 0;
        transform: scale(40, 40);
    }
}

/* 选中状态 */
.seed-btn.selected, .shop-item-btn.selected {
    background-color: #c0c0c0;
    border-color: #4CAF50;
    color: #2E7D32;
}

/* 添加到购物车提示 */
.add-to-cart-hint {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(76, 175, 80, 0.9);
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 14px;
    z-index: 2000;
    animation: fadeInOut 1.5s ease-in-out forwards;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translate(-50%, 20px); }
    20% { opacity: 1; transform: translate(-50%, 0); }
    80% { opacity: 1; transform: translate(-50%, 0); }
    100% { opacity: 0; transform: translate(-50%, -20px); }
}

/* 商店布局 */
.shop-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.shop-items-container {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.shop-section {
    margin-bottom: 20px;
}

.shop-section-title {
    font-weight: bold;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}

.cart-preview {
    background-color: #f5f5f5;
    padding: 10px;
    border-top: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-preview-total {
    font-weight: bold;
    color: #4CAF50;
}

.cart-preview-button {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    font-size: 14px;
}

/* 购物车样式 */
.cart-items {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 4px;
}

.empty-cart {
    text-align: center;
    color: #999;
    padding: 20px 0;
}

.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.cart-item-name {
    flex: 1;
}

.cart-item-price {
    margin: 0 10px;
    color: #b8860b;
}

.cart-item-quantity {
    display: flex;
    align-items: center;
}

.quantity-btn {
    width: 25px;
    height: 25px;
    background-color: #f0f0f0;
    border: none;
    border-radius: 3px;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-value {
    margin: 0 5px;
    width: 20px;
    text-align: center;
}

.cart-item-remove {
    margin-left: 10px;
    color: #cc0000;
    font-size: 16px;
    background: none;
    border: none;
}

.cart-total {
    text-align: right;
    font-weight: bold;
    margin-bottom: 15px;
}

#cart-total-amount {
    color: #b8860b;
}

.cart-buttons {
    display: flex;
    justify-content: space-between;
}

#checkout-btn, #clear-cart-btn {
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
}

#checkout-btn {
    background-color: #4CAF50;
    color: white;
    flex: 2;
    margin-right: 10px;
}

#clear-cart-btn {
    background-color: #f5f5f5;
    color: #666;
    flex: 1;
}

/* 小篮子选择面板 */
.basket-select-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #f2f2f2;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    z-index: 1001;
    display: none;
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.basket-select-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.basket-select-title {
    font-weight: bold;
    font-size: 16px;
    color: #4a4a4a;
}

.basket-select-content {
    padding: 15px;
}

.basket-select-info {
    margin-bottom: 15px;
    padding: 8px 12px;
    background-color: #e9e9e9;
    border-radius: 5px;
    font-size: 14px;
}

.seed-selection-section {
    margin-bottom: 20px;
}

.seed-section-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #4a4a4a;
    font-size: 14px;
}

.available-seeds .seed-btn {
    background-color: #e0e0e0;
    color: #4a4a4a;
    position: relative;
    padding-left: 25px;
}

.unavailable-seeds .seed-btn {
    background-color: #f0f0f0;
    color: #999;
    position: relative;
    padding-left: 25px;
}

.unavailable-seeds .seed-btn::after {
    content: "↗";
    position: absolute;
    top: 2px;
    right: 4px;
    font-size: 12px;
    color: #666;
}

/* 种子图标样式 */
.seed-btn::before {
    position: absolute;
    left: 4px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
}

/* 各种种子的图标 */
.seed-btn[data-seed="五味子"]::before { content: "🫐"; }
.seed-btn[data-seed="乌梅"]::before { content: "🟫"; }
.seed-btn[data-seed="山楂"]::before { content: "🔴"; }
.seed-btn[data-seed="陈皮"]::before { content: "🍊"; }
.seed-btn[data-seed="甘草"]::before { content: "🌿"; }
.seed-btn[data-seed="桂花"]::before { content: "🌼"; }
.seed-btn[data-seed="大麦"]::before { content: "🌾"; }
.seed-btn[data-seed="菊花"]::before { content: "🌻"; }
.seed-btn[data-seed="金银花"]::before { content: "🌺"; }
.seed-btn[data-seed="决明子"]::before { content: "🌰"; }
.seed-btn[data-seed="枸杞"]::before { content: "🔴"; }
.seed-btn[data-seed="生姜"]::before { content: "🫚"; }
.seed-btn[data-seed="桂圆"]::before { content: "🟤"; }
.seed-btn[data-seed="红枣"]::before { content: "🟤"; }
.seed-btn[data-seed="薄荷"]::before { content: "🌿"; }
.seed-btn[data-seed="玫瑰花"]::before { content: "🌹"; }
.seed-btn[data-seed="洛神花"]::before { content: "🌺"; }
.seed-btn[data-seed="冬瓜"]::before { content: "🥒"; }
.seed-btn[data-seed="荷叶"]::before { content: "🍃"; }
.seed-btn[data-seed="薏米"]::before { content: "⚪"; }
.seed-btn[data-seed="雪花梨"]::before { content: "🍐"; }
.seed-btn[data-seed="话梅"]::before { content: "🟫"; }
.seed-btn[data-seed="甘蔗"]::before { content: "🌾"; }
.seed-btn[data-seed="柚子"]::before { content: "🍋"; }
.seed-btn[data-seed="柠檬"]::before { content: "🍋"; }

.basket-select-buttons {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.basket-select-buttons button {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
}

#confirm-plant-btn {
    background-color: #4CAF50;
    color: white;
}

#confirm-plant-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

#cancel-plant-btn {
    background-color: #e0e0e0;
    color: #4a4a4a;
}

/* 配方面板 */
.recipe-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    background-color: #f2f2f2;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    display: none;
    overflow: hidden;
    flex-direction: column;
    -webkit-overflow-scrolling: touch;
}



.recipe-items {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 15px;
    -webkit-overflow-scrolling: touch;
}

.recipe-item {
    padding: 12px;
    margin-bottom: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.recipe-item:hover {
    background-color: #f0f0f0;
}

.recipe-name {
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
    font-size: 14px;
}

.recipe-name::before {
    content: "🍵 ";
    font-size: 16px;
}

.recipe-ingredients {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}



/* 游戏区域 */
.game-content {
    padding: 10px 0;
    flex: 1;
    overflow-y: auto;
    max-height: calc(100vh - 150px);
}

/* 农田区域 */
.farm-controls {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.farm-controls button {
    flex: 1;
    padding: 8px 5px;
    margin: 0 4px;
    border: none;
    border-radius: 4px;
    background-color: #f0f0f0;
    font-size: 12px;
}

.plot-selectors {
    margin-bottom: 10px;
}

.plots-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.plot {
    flex: 0 1 calc(50% - 10px);
    background-color: #e9e9e9;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 10px;
}

.plot-checkbox-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}

.plot-checkbox-container label {
    margin-left: 4px;
    font-weight: bold;
    font-size: 13px;
}

.plot-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.plot-row {
    display: flex;
}

.plot-label {
    flex: 1;
    font-weight: bold;
}

.plot-value {
    flex: 2;
}

/* 厨房区域 */
.section-title {
    position: relative;
    background-color: #f0f0f0;
    padding: 8px;
    margin-bottom: 8px;
    border-radius: 4px;
    font-weight: bold;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 34px;
    font-size: 14px;
}

.stoves {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    margin-bottom: 15px;
}

.stove {
    flex: 1;
    height: 70px;
    background-color: #e0e0e0;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    padding: 5px;
}

.stove-label {
    font-size: 11px;
    font-weight: bold;
    color: #666;
    margin-bottom: 2px;
}

.processing-board {
    height: 70px;
    background-color: #e0e0e0;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 12px;
    margin-bottom: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    padding: 5px;
}

.processing-label {
    font-size: 11px;
    font-weight: bold;
    color: #666;
    margin-bottom: 2px;
}

.processing-recipes {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.recipe-btn {
    flex: 1;
    padding: 8px 4px;
    margin: 2px;
    background-color: #e9e9e9;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    color: #4a4a4a;
    font-size: 12px;
    text-align: center;
    transition: all 0.2s ease;
    position: relative;
    min-height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
}

/* 原料不足状态（灰色） */
.recipe-btn.no-material {
    background-color: #f0f0f0 !important;
    border-color: #ccc !important;
    color: #999 !important;
    cursor: not-allowed;
    opacity: 0.7;
}

/* 原料不多状态（橙色警告） */
.recipe-btn.low-material {
    background-color: #fff3e0 !important;
    border-color: #ffab40 !important;
    color: #ff6b00 !important;
    font-weight: 600;
    animation: pulse 2s infinite;
}

/* 原料充足状态（绿色） */
.recipe-btn.enough-material {
    background-color: #e8f5e8 !important;
    border-color: #4caf50 !important;
    color: #2e7d32 !important;
    font-weight: 600;
}

@keyframes pulse {
    0%, 100% { 
        box-shadow: 0 0 0 0 rgba(255, 107, 0, 0.4);
    }
    50% { 
        box-shadow: 0 0 0 4px rgba(255, 107, 0, 0);
    }
}

.recipe-btn[data-recipe="红糖"]::before {
    content: "🌾 ";
}

.recipe-btn[data-recipe="薄荷叶"]::before {
    content: "🌿 ";
}

.recipe-btn[data-recipe="姜丝"]::before {
    content: "🫚 ";
}

.recipe-btn[data-recipe="柚子丝"]::before {
    content: "🍊 ";
}

.recipe-btn[data-recipe="银耳丝"]::before {
    content: "🍄 ";
}

.recipe-btn[data-recipe="柠檬片"]::before {
    content: "🍋 ";
}

.basket-content {
    max-height: 150px;
    overflow-y: auto;
    background-color: #f9f9f9;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.basket-item {
    padding: 8px;
    margin-bottom: 6px;
    border-radius: 4px;
    font-size: 12px;
    position: relative;
    background-color: #f0f0f0;
    transition: all 0.2s ease;
}

.basket-item:last-child {
    margin-bottom: 0;
}

/* 茶摊区域 */
.tea-display {
    height: 200px;
    background-color: #e9e9e9;
    border-radius: 5px;
    padding: 10px;
    overflow-x: auto;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    white-space: nowrap;
}

.no-tea {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
}

.tea-item {
    display: inline-block;
    width: 100px;
    height: 160px;
    padding: 10px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    text-align: center;
    position: relative;
}

.tea-name {
    font-weight: bold;
    margin-bottom: 4px;
    font-size: 12px;
}

.tea-temp {
    font-size: 11px;
    margin-bottom: 4px;
}

/* 加料样式 */
.tea-toppings {
    font-size: 11px;
    margin-bottom: 4px;
    color: #7a7a7a;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 33px;
}

.hot-tea {
    color: #808080;
}

.cold-tea {
    color: #a9a9a9;
}

.tea-actions {
    position: absolute;
    bottom: 10px;
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 0 5px;
    margin-top: 10px;
}

.tea-action-btn {
    padding: 3px 6px;
    font-size: 11px;
    border: none;
    border-radius: 3px;
    background-color: #d9d9d9;
    color: #4a4a4a;
    cursor: pointer;
    width: 100%;
}

.tea-action-btn.serve-tea {
    background-color: #d9d9d9;
}

.tea-action-btn.serve-tea:active {
    background-color: #c0c0c0;
}

.tea-action-btn.add-topping {
    background-color: #d9d9d9;
}

.tea-action-btn.add-topping:active {
    background-color: #c0c0c0;
}

.toppings-display {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    padding: 8px 12px;
    background-color: #f9f9f9;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    justify-content: space-around;
    align-content: flex-start;
}

.topping-item {
    background-color: #e0e0e0;
    border: 1px solid #d0d0d0;
    border-radius: 20px;
    padding: 8px 12px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    flex: 0 1 calc(33.333% - 8px);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    min-width: 90px;
    max-width: 120px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    gap: 4px;
    margin-bottom: 4px;
}

.topping-item:hover {
    background-color: #d0d0d0;
    transform: translateY(-1px);
}

.topping-item:active {
    transform: translateY(0);
    background-color: #c0c0c0;
}

.topping-name {
    font-weight: bold;
    font-size: 11px;
    color: #4a4a4a;
    margin: 0;
    line-height: 1.2;
    position: relative;
    white-space: nowrap;
}

/* 小料图标 - 通过JavaScript动态添加 */
.topping-icon {
    display: inline-block;
    font-size: 16px;
    margin: 0;
    flex-shrink: 0;
}

.topping-count {
    font-size: 10px;
    color: #4a4a4a;
    background-color: rgba(255,255,255,0.9);
    border-radius: 10px;
    padding: 2px 6px;
    min-width: 24px;
    font-weight: bold;
    text-align: center;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    flex-shrink: 0;
}

/* 响应式调整 */
@media (max-width: 320px) {
    body {
        font-size: 13px;
    }
    
    .seed-grid, .item-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .topping-item {
        flex: 1 0 100%;
    }
    
    .game-content {
        max-height: calc(100vh - 120px);
    }
    
    /* 小屏幕配方网格调整 */
    .recipe-items {
        grid-template-columns: 1fr;
        gap: 8px;
    }
}

/* 添加中等尺寸手机的适配 */
@media (max-width: 480px) {
    .game-content {
        max-height: calc(100vh - 130px);
        -webkit-overflow-scrolling: touch;
    }
    
    .container {
        padding: 5px;
    }
    
    .plot {
        padding: 8px;
    }
    
    /* 中等屏幕配方网格调整 */
    .recipe-items {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 8px;
    }
}

/* 购物车列表样式 */
.cart-list {
    margin: 10px 0;
    max-height: 150px;
    overflow-y: auto;
    background-color: white;
    border-radius: 4px;
    padding: 5px;
}

.cart-item-preview {
    padding: 5px;
    border-bottom: 1px solid #eee;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-item-preview:last-child {
    border-bottom: none;
}

.cart-preview {
    background-color: #f5f5f5;
    padding: 10px;
    border-top: 1px solid #ddd;
}

.cart-preview-total {
    font-weight: bold;
    margin-bottom: 10px;
}

.cart-preview-button {
    width: 100%;
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 10px;
    border-radius: 4px;
    font-size: 16px;
    margin-top: 10px;
}

.cart-preview-button:active {
    background-color: #3d8b40;
}

/* 农田和厨房集成布局 */
.farm-kitchen-layout {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 15px;
}

@media (min-width: 600px) {
    .farm-kitchen-layout {
        flex-direction: row;
    }
    
    .farm-section {
        flex: 3;
        margin-right: 10px;
    }
    
    .kitchen-section {
        flex: 2;
    }
}

.farm-section, .kitchen-section {
    background-color: #f9f9f9;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* 快速操作栏 */
.quick-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.quick-action-btn {
    flex: 1;
    padding: 10px;
    background-color: #a8a8a8;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
}

.quick-action-btn:active {
    background-color: #888888;
}

/* 统一农田并排显示 */
.plots-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.plot {
    flex: 0 0 calc(50% - 5px);
    background-color: #e9e9e9;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 0;
}

/* 统一按钮样式 */
.shop-corner-btn, .recipe-corner-btn, .serve-corner-btn, .recipe-btn {
    background-color: #f0f0f0;
    color: #666666;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 14px;
    cursor: pointer;
}

.shop-corner-btn:active, .recipe-corner-btn:active, .serve-corner-btn:active, .recipe-btn:active {
    background-color: #e0e0e0;
}

/* 右上角商店按钮样式 */
.shop-corner-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 10px;
    background-color: #f0f0f0;
    color: #666666;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 14px;
    cursor: pointer;
    z-index: 10;
    line-height: 1.2;
    height: 28px;
    display: flex;
    align-items: center;
}

.shop-corner-btn:active {
    background-color: #e0e0e0;
}

/* 左上角配方按钮样式 */
.recipe-corner-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 10px;
    background-color: #f0f0f0;
    color: #666666;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 14px;
    cursor: pointer;
    z-index: 10;
    line-height: 1.2;
    height: 28px;
    display: flex;
    align-items: center;
}

.recipe-corner-btn:active {
    background-color: #e0e0e0;
}

/* 服务顾客按钮样式 */
.serve-corner-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 5px; /* 在商店按钮左侧 */
    background-color: #888888;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 14px;
    cursor: pointer;
    z-index: 10;
    line-height: 1.2;
    height: 28px;
    display: flex;
    align-items: center;
}

.serve-corner-btn:active {
    background-color: #666666;
}

/* 菜单按钮样式 */
.menu-panel button, #buy-seed-farm, #water-farm, #fertilize-farm, #dig-out-farm {
    background-color: #888888;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    text-align: center;
}

/* 购物车空提示 */
.cart-empty-msg {
    text-align: center;
    color: #999;
    padding: 10px 0;
}

/* 种子项目样式 */
.seed-item {
    background-color: #f0f0f0 !important;
    border-radius: 4px;
    padding: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 库存物品样式 */
.inventory-item {
    background-color: #f0f0f0 !important;
    border-radius: 4px;
    padding: 6px;
}

.item-name {
    font-weight: bold;
    font-size: 12px;
}

.item-count {
    color: #666;
    font-size: 11px;
}

/* 种植双击提示 */
.seed-item:after {
    content: '';
    position: absolute;
    right: 8px;
    color: #4CAF50;
    font-size: 10px;
    opacity: 0.7;
}

/* 物品点击提示 */
.inventory-item:after {
    content: '';
    position: absolute;
    right: 8px;
    color: #2196F3;
    font-size: 10px;
    opacity: 0.7;
}

/* 高亮动画效果 */
@keyframes highlight {
    0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(76, 175, 80, 0); }
    100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
}

/* 可点击的农田元素 */
.clickable-stat {
    position: relative;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.plot-moisture.clickable-stat {
    background-color: rgba(33, 150, 243, 0.1);
}

.plot-moisture.clickable-stat:hover,
.plot-moisture.clickable-stat:active {
    background-color: rgba(33, 150, 243, 0.2);
}

.plot-fertility.clickable-stat {
    background-color: rgba(76, 175, 80, 0.1);
}

.plot-fertility.clickable-stat:hover,
.plot-fertility.clickable-stat:active {
    background-color: rgba(76, 175, 80, 0.2);
}

/* 提示图标 */
.clickable-stat::after {
    content: '↑';
    font-size: 10px;
    margin-left: 2px;
    color: #666;
}

/* 双击地块提示 */
.plot {
    position: relative;
}

.plot::before {
    content: '双击挖出';
    position: absolute;
    top: 4px;
    right: 4px;
    font-size: 9px;
    color: #999;
    opacity: 0.8;
    pointer-events: none;
}

/* 可收获提示 */
.plot-timer:empty:before {
    content: '';
}

.plot-timer:empty {
    position: relative;
}

.plot-timer:empty::after {
    content: '点击收获';
    color: #4CAF50;
    font-weight: bold;
    font-size: 11px;
}

/* 炉灶配方选择面板 */
.recipe-select-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    background-color: #f2f2f2;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    display: none; /* 确保面板默认隐藏 */
    overflow: hidden;
    flex-direction: column;
}

.recipe-select-header {
    padding: 10px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.recipe-select-content {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    max-height: 60vh;
    -webkit-overflow-scrolling: touch;
}

.recipe-select-list {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 15px;
}

.recipe-item-select {
    padding: 12px;
    margin-bottom: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.recipe-item-select.selected {
    background-color: #e8f5e9;
    border-left: 3px solid #4CAF50;
}

.recipe-item-select:hover {
    background-color: #f0f0f0;
}

.recipe-select-description {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
}

#selected-recipe-name {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 6px;
}

#selected-recipe-ingredients {
    font-size: 12px;
    color: #666;
}

.ingredient-available {
    color: #4CAF50;
}

.ingredient-missing {
    color: #F44336;
}

.recipe-select-buttons {
    display: flex;
    gap: 10px;
}

.recipe-select-buttons button {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
}

#make-recipe-btn {
    background-color: #4CAF50;
    color: white;
}

#make-recipe-btn:disabled {
    background-color: #ccc;
    color: #666;
}

#cancel-recipe-btn {
    background-color: #f0f0f0;
}

/* 顾客通知样式 */
.customer-notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(92, 184, 92, 0.9);
    color: white;
    padding: 15px 20px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    z-index: 2000;
    max-width: 80%;
    text-align: center;
    animation: slideDown 0.3s ease-out;
}

.customer-notification.fadeout {
    animation: fadeOut 0.5s ease-out forwards;
}

@keyframes slideDown {
    0% { transform: translate(-50%, -50px); opacity: 0; }
    100% { transform: translate(-50%, 0); opacity: 1; }
}

@keyframes fadeOut {
    0% { opacity: 1; }
    100% { opacity: 0; }
}

/* 删除未使用的种子信息样式 */
.seed-info {
    display: none;
}

/* 配方列表样式 */
.recipe-items {
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
}

#tea-info-panel {
    width: 100%;
    min-height: 40px;
    margin-bottom: 10px;
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tea-info-msg {
    color: #2e7d32;
    font-size: 13px;
    font-weight: bold;
    animation: fadeInTeaInfo 0.5s;
    transition: opacity 0.5s;
    white-space: pre-line; /* 支持显示换行符 */
}

@keyframes fadeInTeaInfo {
    from { 
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 集卡面板样式 */
.collection-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.collection-header {
    padding: 16px;
    background: #f8f8f8;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.collection-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.collection-content {
    padding: 16px;
    overflow-y: auto;
    flex: 1;
}

.collection-card {
    background: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
    transition: transform 0.2s;
}

.collection-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-name {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    margin-bottom: 6px;
}

.card-count {
    font-size: 12px;
    color: #666;
    margin-bottom: 3px;
}

.card-visit {
    font-size: 10px;
    color: #999;
}

.no-cards {
    text-align: center;
    color: #999;
    padding: 32px 0;
}

/* 植物图标样式 */
.plot-icon {
    margin-left: 5px;
    font-size: 16px;
}

/* 炉灶和案板区域卡片样式 */
.stoves-container, .processing-container {
    background-color: #f9f9f9;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 15px;
}

/* 特别为消息记录滑块设置更紧凑的高度 */
.swiper-slide.message-log {
    height: auto;
    max-height: 120px;
}

/* 茶摊区域卡片样式 */
.tea-container {
    background-color: #f9f9f9;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 15px;
    /* 添加可滚动特性，确保长内容在小屏幕上可滚动查看 */
    max-height: 80vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

/* 小料标题区域样式 */
.tea-container .toppings-header {
    background-color: transparent;
    box-shadow: none;
    margin-top: 0;
    margin-bottom: 10px;
    padding: 0;
    /* 固定顶部，使其始终可见 */
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #f9f9f9;
}

.toppings-header .toppings-display {
    background-color: #e9e9e9;
    border-radius: 8px;
    padding: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    min-height: 60px;
}

/* 针对小屏幕调整茶摊区域高度 */
@media (max-width: 480px) {
    .tea-container {
        max-height: 60vh;
    }
    
    .tea-display {
        padding: 5px;
    }
    
    .tea-item {
        padding: 8px;
        margin-bottom: 6px;
    }
    
    .topping-item {
        flex: 0 1 calc(50% - 6px);
        padding: 6px 8px;
        min-width: 80px;
        max-width: 100px;
        gap: 3px;
    }
    
    .toppings-display {
        gap: 4px;
        padding: 6px 8px;
    }
}

/* 添加游戏状态信息的字体大小 */
.game-status {
    font-size: 14px !important; /* 从13px调大到14px */
    line-height: 1.3;
}

/* 确保信息面板中的所有文字都较小 */
.weather-season .customer-info-row,
.weather-season .info-label,
.weather-season #customer-name,
.weather-season #customer-tea,
.weather-season #customer-toppings,
.weather-season #patience-timer {
    font-size: 14px !important; /* 从13px调整为14px */
}

/* 测试模式样式 */
.test-mode-panel {
    position: fixed;
    top: 20%;
    left: 15%;
    width: 70%;
    max-width: 250px; /* 更小的最大宽度 */
    max-height: 50%; /* 更低的高度 */
    background-color: #e8e8e8; /* 更灰的背景 */
    border-radius: 6px; /* 更小的圆角 */
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.15); /* 更轻的阴影 */
    z-index: 1001;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: none; /* 禁用过渡效果，确保拖动时不延迟 */
}

.test-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 10px; /* 更小的内边距 */
    background-color: #999; /* 更灰的颜色 */
    color: white;
    cursor: move; /* 指示可拖动 */
}

.test-title {
    font-size: 12px; /* 更小的字体 */
    font-weight: bold;
}

.test-content {
    padding: 8px; /* 更小的内边距 */
    overflow-y: auto;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 6px; /* 更小的间距 */
}

.test-instruction {
    background-color: #d5d5d5; /* 更灰的背景 */
    padding: 5px 6px; /* 更小的内边距 */
    border-radius: 3px; /* 更小的圆角 */
    border-left: 2px solid #999; /* 更灰的边框 */
    margin-bottom: 4px; /* 更小的下边距 */
    font-size: 11px; /* 更小的字体 */
}

.test-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 4px; /* 更小的间距 */
    margin-bottom: 6px; /* 更小的下边距 */
}

.test-controls button {
    flex: 1;
    min-width: 60px; /* 更小的最小宽度 */
    padding: 4px 6px; /* 更小的内边距 */
    border: none;
    border-radius: 3px; /* 更小的圆角 */
    background-color: #aaa; /* 更灰的颜色 */
    color: white;
    font-weight: normal; /* 去掉粗体 */
    font-size: 10px; /* 更小的字体 */
    cursor: pointer;
    transition: background-color 0.2s;
}

.test-controls button:hover {
    background-color: #888; /* 悬停时更深的灰色 */
}

#exit-test-mode {
    background-color: #bbb; /* 退出按钮也是灰色 */
}

#exit-test-mode:hover {
    background-color: #999; /* 悬停时更深的灰色 */
}

.test-status {
    background-color: #d5d5d5; /* 更灰的背景 */
    padding: 4px 6px; /* 更小的内边距 */
    border-radius: 3px; /* 更小的圆角 */
    margin-top: auto;
    font-size: 10px; /* 更小的字体 */
}

/* 测试模式指示器 */
.test-mode-indicator {
    position: fixed;
    top: 60px;
    right: 10px;
    background-color: #ff9800;
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.8rem;
    z-index: 1000;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* 菜单暂停按钮样式 */
.menu-pause-btn {
    position: relative;
}

.menu-pause-btn.active::before {
    content: "继续游戏";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #4CAF50;
    color: white;
}

/* 暂停遮罩 */
.pause-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.pause-overlay.active {
    display: flex;
}

.resume-btn {
    margin-top: 20px;
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 18px;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
} 