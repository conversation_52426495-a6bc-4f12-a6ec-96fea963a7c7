<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍵 茶铺测试中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.15);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50, #81C784);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 1.8em;
            margin-bottom: 8px;
        }

        .content {
            padding: 20px;
        }

        .section {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 4px solid #4CAF50;
        }

        .section h2 {
            color: #2E7D32;
            margin-bottom: 15px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
        }

        .test-btn {
            background: linear-gradient(135deg, #4CAF50, #66BB6A);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 3px 6px rgba(76, 175, 80, 0.3);
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 10px rgba(76, 175, 80, 0.4);
        }

        .test-btn.danger {
            background: linear-gradient(135deg, #f44336, #ef5350);
        }

        .test-btn.info {
            background: linear-gradient(135deg, #2196F3, #42A5F5);
        }

        .test-btn.special {
            background: linear-gradient(135deg, #9C27B0, #BA68C8);
        }

        .status-panel {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 2px solid #e0e0e0;
            margin-bottom: 15px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            font-weight: bold;
            color: #333;
        }

        .status-value {
            color: #4CAF50;
            font-weight: bold;
        }

        .log-panel {
            background: #263238;
            color: #4CAF50;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 3px;
        }

        .log-time {
            color: #81C784;
            margin-right: 8px;
        }

        .customer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 8px;
        }

        .customer-btn {
            background: linear-gradient(135deg, #9C27B0, #BA68C8);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .customer-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 6px rgba(156, 39, 176, 0.3);
        }

        .recipe-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
            max-height: 300px;
            overflow-y: auto;
        }

        .recipe-card {
            background: white;
            border-radius: 8px;
            padding: 12px;
            border: 2px solid #e0e0e0;
            font-size: 12px;
        }

        .recipe-card.unlocked {
            border-color: #4CAF50;
            background: #f1f8e9;
        }

        .recipe-card.locked {
            opacity: 0.6;
        }

        .recipe-name {
            font-weight: bold;
            margin-bottom: 5px;
            color: #2E7D32;
        }

        .recipe-ingredients {
            color: #666;
            margin-bottom: 3px;
        }

        .recipe-unlock {
            color: #888;
            font-style: italic;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🍵 茶铺测试中心</h1>
            <p>快速测试游戏功能和配方解锁</p>
        </div>

        <div class="content">
            <!-- 游戏状态 -->
            <div class="section">
                <h2>📊 游戏状态</h2>
                <div class="status-panel" id="status-panel">
                    <div class="status-item">
                        <span class="status-label">💰 金币</span>
                        <span class="status-value" id="coins-status">0</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">👥 已服务顾客</span>
                        <span class="status-value" id="customers-status">0</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">📖 已解锁配方</span>
                        <span class="status-value" id="recipes-status">0</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">🌤️ 当前天气</span>
                        <span class="status-value" id="weather-status">晴天</span>
                    </div>
                </div>
            </div>

            <!-- 快速测试 -->
            <div class="section">
                <h2>⚡ 快速测试</h2>
                <div class="button-grid">
                    <button class="test-btn" onclick="addCoins(1000)">💰 增加1000金币</button>
                    <button class="test-btn" onclick="addAllMaterials()">🌿 添加所有材料</button>
                    <button class="test-btn" onclick="addAllToppings()">🍯 添加所有小料</button>
                    <button class="test-btn" onclick="unlockAllRecipes()">📖 解锁所有配方</button>
                    <button class="test-btn info" onclick="changeWeather()">🌦️ 切换天气</button>
                    <button class="test-btn info" onclick="changeSeason()">🍂 切换季节</button>
                    <button class="test-btn danger" onclick="resetGame()">🔄 重置游戏</button>
                    <button class="test-btn special" onclick="refreshStatus()">🔄 刷新状态</button>
                </div>
            </div>

            <!-- VIP顾客测试 -->
            <div class="section">
                <h2>👑 VIP顾客测试</h2>
                <div class="customer-grid" id="vip-customers">
                    <!-- VIP顾客按钮将在这里生成 -->
                </div>
                <div style="margin-top: 15px;">
                    <button class="test-btn special" onclick="testAllStories()">🎭 测试所有故事</button>
                    <button class="test-btn info" onclick="resetVisits()">🔄 重置访问记录</button>
                    <button class="test-btn info" onclick="showVisits()">📋 查看访问记录</button>
                </div>
            </div>

            <!-- 配方状态 -->
            <div class="section">
                <h2>📜 配方状态</h2>
                <div class="recipe-grid" id="recipe-status">
                    <!-- 配方状态将在这里显示 -->
                </div>
            </div>

            <!-- 测试日志 -->
            <div class="section">
                <h2>📝 测试日志</h2>
                <div class="log-panel" id="test-log">
                    <div class="log-entry">
                        <span class="log-time">[启动]</span>
                        <span>测试窗口已加载...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 游戏数据
        let gameData = null;
        let gameLoaded = false;

        // 初始化
        function init() {
            try {
                // 尝试从主窗口获取数据
                if (window.opener && window.opener.gameData) {
                    gameData = window.opener.gameData;
                    gameLoaded = true;
                    log('✅ 从主窗口获取游戏数据');
                } else {
                    // 尝试从localStorage获取
                    const saved = localStorage.getItem('cuteTeaShop_save') || localStorage.getItem('teaShopGameData');
                    if (saved) {
                        gameData = JSON.parse(saved);
                        gameLoaded = true;
                        log('✅ 从本地存储获取游戏数据');
                    } else {
                        // 创建默认数据
                        createDefaultData();
                        gameLoaded = true;
                        log('⚠️ 创建默认测试数据');
                    }
                }

                initVIPCustomers();
                refreshStatus();
                log('🎉 测试窗口初始化完成');
            } catch (error) {
                log('❌ 初始化失败: ' + error.message);
                createDefaultData();
                gameLoaded = true;
                initVIPCustomers();
                refreshStatus();
            }
        }

        // 创建默认数据
        function createDefaultData() {
            gameData = {
                coins: 100,
                servedCustomers: 0,
                unlockedRecipes: ["五味子饮", "柠檬茶"],
                customerVisits: {},
                currentWeather: "晴天",
                currentSeason: "春天",
                inventory: {},
                toppings: {},
                recipeUnlockRules: {
                    "洛神玫瑰饮": { customer: "凌小路", visitsRequired: 1, chance: 1.0, guaranteedOnVisit: 1 },
                    "桂圆红枣茶": { customer: "花花", visitsRequired: 1, chance: 1.0, guaranteedOnVisit: 1 },
                    "焦香大麦茶": { customer: "江飞飞", visitsRequired: 2, chance: 1.0, guaranteedOnVisit: 2 },
                    "三花决明茶": { customer: "江三", visitsRequired: 2, chance: 1.0, guaranteedOnVisit: 2 },
                    "薄荷甘草凉茶": { customer: "江四", visitsRequired: 2, chance: 1.0, guaranteedOnVisit: 2 },
                    "陈皮姜米茶": { customer: "池云旗", visitsRequired: 2, chance: 0.5, guaranteedOnVisit: 3 },
                    "冬瓜荷叶饮": { customer: "江潮", visitsRequired: 3, chance: 0.6, guaranteedOnVisit: 4 },
                    "古法酸梅汤": { customer: "池惊暮", visitsRequired: 2, chance: 0.3, guaranteedOnVisit: 3 },
                    "小吊梨汤": { customer: "江敕封", visitsRequired: 3, chance: 0.4, guaranteedOnVisit: 5 }
                }
            };

            // 初始化材料
            const materials = ["五味子", "乌梅", "山楂", "陈皮", "甘草", "桂花", "大麦", "菊花", "金银花", "决明子", "枸杞", "生姜", "桂圆", "红枣", "薄荷", "玫瑰花", "洛神花", "冬瓜", "荷叶", "薏米", "雪花梨", "话梅", "甘蔗", "柚子", "柠檬", "桑叶", "杭白菊", "水蜜桃", "黄芪", "白茅根", "马蹄", "糯米", "米", "银耳"];
            materials.forEach(m => gameData.inventory[m] = 1);

            // 初始化小料
            const toppings = ["红糖", "薄荷叶", "姜丝", "柚子丝", "银耳丝", "柠檬片", "蜂蜜", "冰糖", "乌龙茶包", "干桂花", "小圆子", "酒酿", "水蜜桃果肉", "黄芪片"];
            toppings.forEach(t => gameData.toppings[t] = 5);
        }

        // 日志函数
        function log(message) {
            const logPanel = document.getElementById('test-log');
            const time = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `<span class="log-time">[${time}]</span><span>${message}</span>`;
            logPanel.appendChild(entry);
            logPanel.scrollTop = logPanel.scrollHeight;
        }

        // 初始化VIP顾客按钮
        function initVIPCustomers() {
            const container = document.getElementById('vip-customers');
            const vipCustomers = [
                { name: '凌小路', recipe: '洛神玫瑰饮' },
                { name: '花花', recipe: '桂圆红枣茶' },
                { name: '江飞飞', recipe: '焦香大麦茶' },
                { name: '江三', recipe: '三花决明茶' },
                { name: '江四', recipe: '薄荷甘草凉茶' },
                { name: '池云旗', recipe: '陈皮姜米茶' },
                { name: '江潮', recipe: '冬瓜荷叶饮' },
                { name: '池惊暮', recipe: '古法酸梅汤' },
                { name: '江敕封', recipe: '小吊梨汤' }
            ];

            container.innerHTML = '';
            vipCustomers.forEach(customer => {
                const button = document.createElement('button');
                button.className = 'customer-btn';
                button.textContent = `👑 ${customer.name}`;
                button.onclick = () => spawnVIPCustomer(customer.name);
                container.appendChild(button);
            });
        }

        // 刷新状态显示
        function refreshStatus() {
            if (!gameLoaded || !gameData) {
                log('❌ 游戏数据未加载');
                return;
            }

            try {
                document.getElementById('coins-status').textContent = gameData.coins || 0;
                document.getElementById('customers-status').textContent = gameData.servedCustomers || 0;
                document.getElementById('recipes-status').textContent = (gameData.unlockedRecipes || []).length;
                document.getElementById('weather-status').textContent = gameData.currentWeather || '晴天';

                updateRecipeStatus();
                log('✅ 状态刷新完成');
            } catch (error) {
                log('❌ 状态刷新失败: ' + error.message);
            }
        }

        // 更新配方状态
        function updateRecipeStatus() {
            const container = document.getElementById('recipe-status');
            const allRecipes = [
                { name: '五味子饮', ingredients: ['五味子', '乌梅', '山楂'], unlockType: '基础配方' },
                { name: '柠檬茶', ingredients: ['柠檬', '蜂蜜'], unlockType: '基础配方' },
                { name: '洛神玫瑰饮', ingredients: ['洛神花', '玫瑰花', '山楂'], unlockType: '凌小路解锁' },
                { name: '桂圆红枣茶', ingredients: ['桂圆', '红枣', '枸杞'], unlockType: '花花解锁' },
                { name: '焦香大麦茶', ingredients: ['大麦'], unlockType: '江飞飞解锁' },
                { name: '三花决明茶', ingredients: ['菊花', '金银花', '决明子', '枸杞'], unlockType: '江三解锁' },
                { name: '薄荷甘草凉茶', ingredients: ['薄荷', '甘草'], unlockType: '江四解锁' },
                { name: '陈皮姜米茶', ingredients: ['陈皮', '生姜'], unlockType: '池云旗解锁' },
                { name: '冬瓜荷叶饮', ingredients: ['冬瓜', '荷叶', '薏米'], unlockType: '江潮解锁' },
                { name: '古法酸梅汤', ingredients: ['乌梅', '山楂', '陈皮', '甘草', '桂花'], unlockType: '池惊暮解锁' },
                { name: '小吊梨汤', ingredients: ['雪花梨', '银耳', '话梅', '枸杞'], unlockType: '江敕封解锁' },
                { name: '桑菊润燥茶', ingredients: ['桑叶', '杭白菊'], unlockType: '30人解锁' },
                { name: '桂花酒酿饮', ingredients: ['桂花', '酒酿'], unlockType: '60人解锁' },
                { name: '蜜桃乌龙冷萃', ingredients: ['水蜜桃', '乌龙茶包'], unlockType: '90人解锁' },
                { name: '黄芪枸杞茶', ingredients: ['黄芪', '枸杞'], unlockType: '120人解锁' },
                { name: '竹蔗茅根马蹄水', ingredients: ['甘蔗', '白茅根', '马蹄'], unlockType: '150人解锁' }
            ];

            container.innerHTML = '';
            allRecipes.forEach(recipe => {
                const isUnlocked = (gameData.unlockedRecipes || []).includes(recipe.name);
                const card = document.createElement('div');
                card.className = `recipe-card ${isUnlocked ? 'unlocked' : 'locked'}`;
                card.innerHTML = `
                    <div class="recipe-name">${isUnlocked ? '✅' : '🔒'} ${recipe.name}</div>
                    <div class="recipe-ingredients">材料: ${recipe.ingredients.join(', ')}</div>
                    <div class="recipe-unlock">${recipe.unlockType}</div>
                `;
                container.appendChild(card);
            });
        }

        // 测试功能
        function addCoins(amount) {
            if (!gameLoaded) return;
            gameData.coins = (gameData.coins || 0) + amount;
            saveData();
            refreshStatus();
            log(`💰 增加了 ${amount} 金币`);
        }

        function addAllMaterials() {
            if (!gameLoaded) return;
            const materials = ["五味子", "乌梅", "山楂", "陈皮", "甘草", "桂花", "大麦", "菊花", "金银花", "决明子", "枸杞", "生姜", "桂圆", "红枣", "薄荷", "玫瑰花", "洛神花", "冬瓜", "荷叶", "薏米", "雪花梨", "话梅", "甘蔗", "柚子", "柠檬", "桑叶", "杭白菊", "水蜜桃", "黄芪", "白茅根", "马蹄", "糯米", "米", "银耳"];
            materials.forEach(material => {
                gameData.inventory[material] = (gameData.inventory[material] || 0) + 10;
            });
            saveData();
            log('🌿 添加了所有材料各10个');
        }

        function addAllToppings() {
            if (!gameLoaded) return;
            const toppings = ["红糖", "薄荷叶", "姜丝", "柚子丝", "银耳丝", "柠檬片", "蜂蜜", "冰糖", "乌龙茶包", "干桂花", "小圆子", "酒酿", "水蜜桃果肉", "黄芪片"];
            toppings.forEach(topping => {
                gameData.toppings[topping] = (gameData.toppings[topping] || 0) + 10;
            });
            saveData();
            log('🍯 添加了所有小料各10个');
        }

        function unlockAllRecipes() {
            if (!gameLoaded) return;
            const allRecipes = ['五味子饮', '柠檬茶', '洛神玫瑰饮', '桂圆红枣茶', '焦香大麦茶', '三花决明茶', '薄荷甘草凉茶', '陈皮姜米茶', '冬瓜荷叶饮', '古法酸梅汤', '小吊梨汤', '桑菊润燥茶', '桂花酒酿饮', '蜜桃乌龙冷萃', '黄芪枸杞茶', '竹蔗茅根马蹄水'];
            gameData.unlockedRecipes = [...allRecipes];
            saveData();
            refreshStatus();
            log(`📖 解锁了所有 ${allRecipes.length} 个配方`);
        }

        function changeWeather() {
            if (!gameLoaded) return;
            const weathers = ["晴天", "刮风", "下雨", "下雪", "阴天"];
            const currentIndex = weathers.indexOf(gameData.currentWeather || "晴天");
            const nextIndex = (currentIndex + 1) % weathers.length;
            gameData.currentWeather = weathers[nextIndex];
            saveData();
            refreshStatus();
            log(`🌦️ 天气切换为: ${gameData.currentWeather}`);
        }

        function changeSeason() {
            if (!gameLoaded) return;
            const seasons = ["春天", "夏天", "秋天", "冬天"];
            const currentIndex = seasons.indexOf(gameData.currentSeason || "春天");
            const nextIndex = (currentIndex + 1) % seasons.length;
            gameData.currentSeason = seasons[nextIndex];
            saveData();
            refreshStatus();
            log(`🍂 季节切换为: ${gameData.currentSeason}`);
        }

        function resetGame() {
            if (!gameLoaded) return;
            if (confirm('确定要重置游戏吗？这将清除所有进度！')) {
                gameData.coins = 100;
                gameData.servedCustomers = 0;
                gameData.unlockedRecipes = ["五味子饮", "柠檬茶"];
                gameData.customerVisits = {};
                gameData.currentWeather = "晴天";
                gameData.currentSeason = "春天";
                Object.keys(gameData.inventory || {}).forEach(key => {
                    gameData.inventory[key] = 1;
                });
                Object.keys(gameData.toppings || {}).forEach(key => {
                    gameData.toppings[key] = 5;
                });
                saveData();
                refreshStatus();
                log('🔄 游戏已重置');
            }
        }

        // VIP顾客测试功能
        function spawnVIPCustomer(customerName) {
            if (!gameLoaded) return;

            gameData.customerVisits = gameData.customerVisits || {};
            gameData.customerVisits[customerName] = (gameData.customerVisits[customerName] || 0) + 1;

            checkRecipeUnlock(customerName);
            saveData();
            refreshStatus();
            log(`👑 生成VIP顾客: ${customerName} (第${gameData.customerVisits[customerName]}次访问)`);
        }

        function checkRecipeUnlock(customerName) {
            if (!gameData.recipeUnlockRules) return;

            const visitCount = gameData.customerVisits[customerName] || 0;

            Object.entries(gameData.recipeUnlockRules).forEach(([recipe, rule]) => {
                if (rule.customer === customerName) {
                    if (gameData.unlockedRecipes.includes(recipe)) {
                        return;
                    }

                    if (visitCount >= rule.visitsRequired) {
                        let shouldUnlock = false;

                        if (visitCount >= rule.guaranteedOnVisit) {
                            shouldUnlock = true;
                        } else if (Math.random() < rule.chance) {
                            shouldUnlock = true;
                        }

                        if (shouldUnlock) {
                            gameData.unlockedRecipes.push(recipe);
                            log(`🔓 解锁新配方: ${recipe}！`);
                            showStory(recipe);
                        }
                    }
                }
            });
        }

        function showStory(recipeName) {
            const stories = {
                "洛神玫瑰饮": { title: "朱砂", content: "凌小路袖中藏着一盏温热的洛神玫瑰饮。'疏肝解郁的，好好学学，飞飞来了就做给他。跟他说就说养颜的茶方子'挑眉笑时，眼底却映着刀光，袍角还沾着血。" },
                "桂圆红枣茶": { title: "无归", content: "花花去凌雪坟前扫墓，手里拿着他最喜欢她给他做的茶。只是这一次只能自己做了。'自己给自己作茶怎么行，这方子给你们，以后我就来这里喝吧'" },
                "焦香大麦茶": { title: "雪夜", content: "长安冬夜，江飞飞蜷在凌雪阁的屋檐上，指尖冻得发僵。江三翻上屋顶，扔来一壶滚烫的大麦茶：'怂样，喝两口。'茶雾氤氲里，他忽然想起幼时第一次握刀，也是这焦苦的甜香压住了颤抖。" },
                "三花决明茶": { title: "夜狩", content: "江四执刀归来，见江三伏案瞌睡，手边一盏凉透的三花决明茶。他轻叹，将外袍披上兄长肩头——却不知昨夜自己任务单上那三个名字，早已被江三的血刃划去。茶渣沉底，如未愈的旧伤。" },
                "薄荷甘草凉茶": { title: "三哥", content: "江四给江三泡的茶，清清凉凉的，他那么爱出汗，肯定喜欢。茶叶刚放下，就听到三哥在院子里训练的刀声，他悄悄探头看了一眼，决定加多一片薄荷叶。" },
                "陈皮姜米茶": { title: "师徒", content: "池云旗心疼那小家伙，以前也不懂自己照顾自己，这茶是她专门给他找医师抄的方子。'别总吃那些乱七八糟的东西，胃疼了可别来找师父'虽然嘴上这么说，她还是悄悄在茶里多加了一片陈皮。" },
                "冬瓜荷叶饮": { title: "师徒2", content: "江潮给师父弄的消暑茶，荷叶是自己趴在池塘边采的，冬瓜也是自己种的。'师父，您尝尝，我按照您说的方法做的'他小心翼翼地端着茶，生怕师父不喜欢，却不知道池云旗早已欣慰地笑了。" },
                "古法酸梅汤": { title: "梅香", content: "长安暑夜，池惊暮执剑伏于屋脊。目标出现时，她正饮尽最后一滴酸梅汤。瓷碗坠地碎响混着喉骨断裂声，梅妃教的小方子——杀人时唇齿间该留着甜味，才不苦。" },
                "小吊梨汤": { title: "琴心", content: "江敕封抚琴时总爱在身边放一盏小吊梨汤，琴声悠扬，茶香袅袅。他说琴如人生，需要慢慢调教；茶如心境，需要细细品味。一曲终了，一盏茶尽，都是这世间最温柔的时光。" }
            };

            const story = stories[recipeName];
            if (story) {
                alert(`🍵 ${recipeName}\n\n📖 ${story.title}\n\n${story.content}`);
                log(`🎭 显示故事: ${recipeName} - ${story.title}`);
            }
        }

        function testAllStories() {
            if (!gameLoaded) return;
            const customers = ['凌小路', '花花', '江飞飞', '江三', '江四', '池云旗', '江潮', '池惊暮', '江敕封'];

            customers.forEach((customer, index) => {
                setTimeout(() => {
                    spawnVIPCustomer(customer);
                }, index * 1000);
            });

            log('🎭 开始测试所有顾客故事 (每秒一个)');
        }

        function resetVisits() {
            if (!gameLoaded) return;
            gameData.customerVisits = {};
            saveData();
            log('🔄 重置了所有顾客访问记录');
        }

        function showVisits() {
            if (!gameLoaded) return;
            const visits = gameData.customerVisits || {};
            let message = '📋 顾客访问记录:\n\n';

            if (Object.keys(visits).length === 0) {
                message += '暂无访问记录';
            } else {
                Object.entries(visits).forEach(([customer, count]) => {
                    message += `${customer}: ${count}次\n`;
                });
            }

            alert(message);
            log('📋 显示了顾客访问记录');
        }

        // 保存数据
        function saveData() {
            if (!gameLoaded || !gameData) return;

            try {
                localStorage.setItem('cuteTeaShop_save', JSON.stringify(gameData));
                localStorage.setItem('teaShopGameData', JSON.stringify(gameData));

                if (window.opener && window.opener.gameData) {
                    Object.assign(window.opener.gameData, gameData);
                    if (typeof window.opener.updateAllDisplays === 'function') {
                        window.opener.updateAllDisplays();
                    }
                }
            } catch (error) {
                log('❌ 保存数据失败: ' + error.message);
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
